// import clsx from "clsx";
// import Image from "next/image";
// import React from "react";

// const OurValues = () => {
//   const contentData = [
//     {
//       imageSrc: "/aboutpage/OurValues.png",
//       alt: "Values",
//       title: "Our Values",
//       description:
//         "We believe in creating jewellery that's timeless, ethical and crafted with care. Every piece reflects our passion for honest craftsmanship and thoughtful design, blending tradition with modern elegance.",
//     },
//     {
//       imageSrc: "/aboutpage/OurPromise.png",
//       alt: "Promise",
//       title: "Our Promise",
//       description:
//         "Our promise is to bring you jewellery that celebrates your story. Through quality, sustainability, and meaningful designs, we help you shine confidently in every moment.",
//     },
//   ];
//   return (
//     <div className="relative p-6 min-h-screen flex items-center justify-center overflow-hidden">
//       {/* Left Swirl as Background */}
//       <div className="absolute left-0 top-1/2 transform -translate-y-1/2 ">
//         <Image
//           src="/aboutpage/LeftSwirl.png"
//           alt="Left swirl image"
//           height={500}
//           width={400}
//         />
//       </div>
//       {/* Right Swirl as Background */}
//       <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
//         <Image
//           src="/aboutpage/RightSwirl.png"
//           alt="Right swirl image"
//           height={500}
//           width={400}
//         />
//       </div>

//       <div className="relative z-10 flex justify-center">
//         <div className="w-full max-w-4xl">
//           {contentData.map((item, index) => {
//             const isOdd = index % 2 !== 0;
//             return (
//               <div
//                 key={index.toString()}
//                 className="grid grid-cols-2 auto-rows-[20rem]"
//               >
//                 <div
//                   className={clsx("h-full relative w-full", isOdd && "order-1")}
//                 >
//                   <Image
//                     src={item.imageSrc}
//                     alt="slksdfls"
//                     fill
//                     className="object-cover"
//                   />
//                 </div>
//                 <div
//                   className={clsx(
//                     "py-4 px-16 flex flex-col justify-start ",
//                     isOdd && "px-16 flex flex-col justify-end items-end "
//                   )}
//                 >
//                   <h2
//                     className={
//                       "font-semibold text-xl md:text-2xl lg:text-[28px]"
//                     }
//                   >
//                     {item.title}
//                   </h2>
//                   <p className="text-custom-textgray text-sm text-justify lg:text-base max-w-sm">
//                     {item.description}
//                   </p>
//                 </div>
//               </div>
//             );
//           })}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default OurValues;

// Second

import clsx from "clsx";
import Image from "next/image";
import React from "react";
import { EllipseLeft, EllipseRight } from "./SvgComponent";

const OurValues = () => {
  const contentData = [
    {
      imageSrc: "/aboutpage/OurValues.png",
      alt: "Values",
      title: "Our Values",
      description:
        "We believe in creating jewellery that's timeless, ethical and crafted with care. Every piece reflects our passion for honest craftsmanship and thoughtful design, blending tradition with modern elegance.",
    },
    {
      imageSrc: "/aboutpage/OurPromise.png",
      alt: "Promise",
      title: "Our Promise",
      description:
        "Our promise is to bring you jewellery that celebrates your story. Through quality, sustainability, and meaningful designs, we help you shine confidently in every moment.",
    },
  ];

  return (
    <div className="relative p-6 min-h-screen flex items-center justify-center overflow-hidden">
      {/* Left Swirl as Background */}
      <div className="absolute left-0 top-64 transform -translate-y-1/2">
        <Image
          src="/aboutpage/LeftSwirl.png"
          alt="Left swirl image"
          height={500}
          width={400}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-64 transform -translate-y-1/2">
        <Image
          src="/aboutpage/RightSwirl.png"
          alt="Right swirl image"
          height={500}
          width={400}
        />
      </div>

      <div className="absolute left-0 top-64 transform -translate-y-1/2">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute -right-56 top-64 transform -translate-y-1/2">
        <EllipseRight />
      </div>

      <div className="relative z-10 flex justify-center">
        <div className="w-full max-w-4xl">
          {contentData.map((item, index) => {
            const isOdd = index % 2 !== 0;
            return (
              <div
                key={index.toString()}
                className="grid grid-cols-1 sm:grid-cols-2 auto-rows-[20rem]"
              >
                <div
                  className={clsx(
                    "h-full relative w-full",
                    isOdd && "order-first sm:order-1"
                  )}
                >
                  <Image
                    src={item.imageSrc}
                    alt={item.alt}
                    fill
                    className="object-cover"
                  />
                </div>
                <div
                  className={clsx(
                    "py-4 px-16 flex flex-col justify-start",
                    isOdd && "px-16 flex flex-col justify-end items-end"
                  )}
                >
                  <h2 className="font-semibold text-xl md:text-2xl lg:text-[28px]">
                    {item.title}
                  </h2>
                  <p className="text-custom-textgray text-sm text-justify lg:text-base max-w-sm">
                    {item.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default OurValues;

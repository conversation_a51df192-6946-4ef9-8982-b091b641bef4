export const EllipseLeft = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="401"
      height="698"
      viewBox="0 0 401 698"
      fill="none"
    >
      <g filter="url(#filter0_f_187_2978)">
        <ellipse
          cx="49.5"
          cy="349"
          rx="111.5"
          ry="109"
          fill="url(#paint0_linear_187_2978)"
          fillOpacity="0.5"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_187_2978"
          x="-302"
          y="0"
          width="703"
          height="698"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="120"
            result="effect1_foregroundBlur_187_2978"
          />
        </filter>
        <linearGradient
          id="paint0_linear_187_2978"
          x1="49.5"
          y1="240"
          x2="49.5"
          y2="458"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D44FDB" />
          <stop offset="1" stopColor="#7D5AEF" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const EllipseRight = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="709"
      height="703"
      viewBox="0 0 709 703"
      fill="none"
    >
      <g filter="url(#filter0_f_361_4550)">
        <ellipse
          cx="354.5"
          cy="351.5"
          rx="114.5"
          ry="111.5"
          fill="url(#paint0_linear_361_4550)"
          fillOpacity="0.5"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_361_4550"
          x="0"
          y="0"
          width="709"
          height="703"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="120"
            result="effect1_foregroundBlur_361_4550"
          />
        </filter>
        <linearGradient
          id="paint0_linear_361_4550"
          x1="354.5"
          y1="240"
          x2="354.5"
          y2="463"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D44FDB" />
          <stop offset="1" stopColor="#7D5AEF" />
        </linearGradient>
      </defs>
    </svg>
  );
};

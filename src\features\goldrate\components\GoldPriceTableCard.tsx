// import clsx from "clsx";
// import React from "react";

// interface GoldPriceTableProps {
//   title: string;
//   lastUpdated: string;
//   headers: string[];
//   items: { purity: string; prices: (string | number)[] }[];
// }

// const GoldPriceTable: React.FC<GoldPriceTableProps> = ({
//   title,
//   lastUpdated,
//   headers,
//   items,
// }) => {
//   return (
//     <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200 ">
//       <div className="text-lg font-semibold text-gray-800">{title}</div>
//       <div className="text-sm text-gray-600 mt-1">{lastUpdated}</div>
//       <table className="w-full mt-4 border-collapse">
//         <thead>
//           <tr>
//             {headers.map((header, index) => (
//               <th
//                 key={index}
//                 className={clsx(
//                   "text-left text-sm font-medium text-gray-700 pb-2 border-b border-gray-200",
//                   index === 0 ? "w-1/4" : "w-1/3"
//                 )}
//               >
//                 {header}
//               </th>
//             ))}
//           </tr>
//         </thead>
//         <tbody>
//           {items.map((item, index) => (
//             <tr
//               key={index}
//               className="border-b border-gray-200 last:border-b-0"
//             >
//               <td className="py-2 text-sm text-gray-800">{item.purity}</td>
//               {item.prices.map((price, idx) => (
//                 <td key={idx} className="py-2 text-sm text-gray-800 text-right">
//                   {price}
//                 </td>
//               ))}
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     </div>
//   );
// };

// export default GoldPriceTable;

import clsx from "clsx";
import React from "react";

interface GoldPriceTableProps {
  title: string;
  lastUpdated: string;
  headers: string[];
  items: { purity: string; prices: (string | number)[] }[];
}

const GoldPriceTable: React.FC<GoldPriceTableProps> = ({
  title,
  lastUpdated,
  headers,
  items,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200 ">
      <div className="text-lg font-semibold text-gray-800">{title}</div>
      <div className="text-sm text-gray-600 mt-1">{lastUpdated}</div>
      <table className="w-full mt-4 border-collapse">
        <thead>
          <tr>
            {headers.map((header, index) => (
              <th
                key={index}
                className={clsx(
                  "text-left text-sm font-medium text-gray-700 pb-2",
                  index === 0 ? "w-1/4" : "w-1/3",
                  "after:content-[''] after:block after:w-[80%] after:h-[1px] after:bg-gray-200 after:mt-1"
                )}
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => (
            <tr key={index}>
              <td className="py-2 text-sm text-gray-800">{item.purity}</td>
              {item.prices.map((price, idx) => (
                <td key={idx} className="py-2 text-sm text-gray-800">
                  {price}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default GoldPriceTable;

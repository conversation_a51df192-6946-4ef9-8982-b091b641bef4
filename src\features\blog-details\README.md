# BlogDetails Component

A dynamic, fully-featured blog details component for displaying rich blog content with support for multiple content types, social sharing, author information, and related posts.

## Features

- **Dynamic Content**: Supports text, images, quotes, and lists
- **Social Sharing**: Built-in sharing functionality for Facebook, Twitter, LinkedIn, and copy link
- **Author Section**: Display author information with bio and avatar
- **Related Posts**: Show related articles with navigation
- **Responsive Design**: Mobile-first responsive layout
- **Customizable**: Flexible props for custom behavior
- **Type Safe**: Full TypeScript support

## Usage

### Basic Usage

```tsx
import { BlogDetails } from "@/features/blog-details/components";

function BlogPage() {
  return <BlogDetails />;
}
```

### Custom Data

```tsx
import { BlogDetails } from "@/features/blog-details/components";
import { BlogDetailPost } from "@/features/blog-details/types";

const customPost: BlogDetailPost = {
  id: 1,
  title: "Your Blog Title",
  excerpt: "Brief description...",
  image: "/your-image.jpg",
  category: "Category",
  date: "Dec 1, 2023",
  readTime: "5 min read",
  author: "Author Name",
  content: "Main content...",
  sections: [
    {
      id: "section-1",
      title: "Section Title",
      content: "Section content...",
      type: "text"
    }
  ]
};

function BlogPage() {
  return <BlogDetails post={customPost} />;
}
```

### With Event Handlers

```tsx
import { BlogDetails } from "@/features/blog-details/components";

function BlogPage() {
  const handleShare = (platform: string, postId: number) => {
    // Custom sharing logic
    console.log(`Sharing post ${postId} on ${platform}`);
  };

  const handleRelatedPostClick = (postId: number) => {
    // Custom navigation logic
    router.push(`/blogs/${postId}`);
  };

  return (
    <BlogDetails
      onShare={handleShare}
      onRelatedPostClick={handleRelatedPostClick}
    />
  );
}
```

## Props

### BlogDetailsProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `post` | `BlogDetailPost` | `defaultBlogPost` | Blog post data |
| `onShare` | `(platform: string, postId: number) => void` | - | Custom share handler |
| `onRelatedPostClick` | `(postId: number) => void` | - | Custom related post click handler |

### BlogDetailPost Interface

```tsx
interface BlogDetailPost extends BlogPost {
  content: string;
  sections?: BlogSection[];
  author: string;
  authorImage?: string;
  authorBio?: string;
  relatedPosts?: BlogPost[];
}
```

### BlogSection Interface

```tsx
interface BlogSection {
  id: string;
  title: string;
  content: string;
  type?: "text" | "image" | "quote" | "list";
  imageUrl?: string;
  listItems?: string[];
}
```

## Content Types

### Text Section
```tsx
{
  id: "text-section",
  title: "Section Title",
  content: "Your text content here...",
  type: "text"
}
```

### Image Section
```tsx
{
  id: "image-section",
  title: "Image Title",
  content: "Image description",
  type: "image",
  imageUrl: "/path/to/image.jpg"
}
```

### Quote Section
```tsx
{
  id: "quote-section",
  title: "Quote Title",
  content: "Your inspirational quote here...",
  type: "quote"
}
```

### List Section
```tsx
{
  id: "list-section",
  title: "List Title",
  content: "Introduction to the list:",
  type: "list",
  listItems: [
    "First item",
    "Second item",
    "Third item"
  ]
}
```

## Styling

The component uses Tailwind CSS classes and follows the project's design system:

- Custom colors: `custom-blue-500`, `custom-red-500`
- Responsive breakpoints: `md:`, `lg:`
- Typography: Lora font family
- Components: Card, Button, Badge from UI library

## Examples

See `src/features/blog-details/examples/BlogDetailsExample.tsx` for comprehensive usage examples.

## Integration

The component is designed to work seamlessly with:

- Next.js App Router
- The existing blog system (`@/features/blogs`)
- The project's UI component library
- TypeScript for type safety

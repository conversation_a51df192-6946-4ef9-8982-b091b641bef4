import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { InputField } from "@/components/ui/form-field";
import { get } from "lodash";
import { Minus, Plus, Trash2 } from "lucide-react";
import Image from "next/image";
import React from "react";

const obj = [
  { title: "hello", description: "Enticing Petite Drop Earrings", price: 200 },
];
const payment = {
  "Sub Total": 200,
  "Product Discount": 200,
  "Total (Incl of all Taxes)": 200,
};
const page = () => {
  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="grid grid-cols-1 col-span-2 gap-4">
          <div>
            {obj.map((item, index) => (
              <Card key={index} className="p-4 bg-[#FCFCFC]">
                <CardContent className="flex items-center gap-4 p-0 relative">
                  <div className="aspect-square relative size-32 bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={"/images/ring.png"}
                      alt={"ring"}
                      fill
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div>
                    <p className="font-semibold text-lg">NPR {item.price}</p>
                    <p className="text-base text-custom-red-500  line-clamp-1">
                      {get(item, "description", "N/A")}
                    </p>
                    <p className="text-xs text-blue-500">SKU-UTWR100334</p>
                    <div className="flex text-sm items-center gap-4">
                      <span>Size: 12</span>
                    </div>
                  </div>
                  <div className="absolute top-0 right-0 flex gap-8 items-center">
                    <div className="flex items-center">
                      <Plus className="size-4 text-custom-red-500 hover:scale-110 cursor-pointer" />
                      <span className="p-1 px-2 mx-2 text-sm rounded-full bg-gray-200">
                        0
                      </span>
                      <Minus className="size-4 text-custom-red-500 hover:scale-110 cursor-pointer" />
                    </div>
                    <Trash2 className="size-4 text-custom-red-500 hover:scale-110 cursor-pointer" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        <div>
          <Card className="p-4">
            <CardTitle className="text-custom-red-500">Order Summary</CardTitle>
            <CardContent className="p-0">
              <Card className="p-2 rounded-md bg-gray-200 gap-1">
                <p className="text-sm">Apply Coupon Code / Promo Code</p>
                <InputField
                  placeholder="Enter coupon code"
                  className="bg-white rounded-md"
                  rightSection={
                    <Badge className="bg-gray-600 cursor-pointer rounded-full">
                      Apply
                    </Badge>
                  }
                />
              </Card>
              <div className="pt-1">
                {Object.entries(payment).map(([key, value], index) => (
                  <div key={index} className="flex justify-between">
                    <p className="text-sm text-gray-600">{key}</p>
                    <p className="text-sm">NPR {value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <div className="w-full py-2 mt-4 bg-[#FCFCFC] border-y px-2 rounded-t-md flex items-center justify-between">
        <div>
          <span>{`Total (2 item): `}</span>
          <span className="text-custom-red-500">NPR 200</span>
        </div>
        <Button
          size="sm"
          className="text-white bg-custom-red-500 hover:bg-custom-red-600 "
        >
          Send Purchase Request
        </Button>
      </div>
    </div>
  );
};

export default page;

import { BlogPost } from "@/features/blogs/types";

// Extended blog post interface for detailed content
export interface BlogDetailPost extends BlogPost {
  content: string;
  sections?: BlogSection[];
  author: string;
  authorImage?: string;
  authorBio?: string;
  relatedPosts?: BlogPost[];
}

export interface BlogSection {
  id: string;
  title: string;
  content: string;
  type?: "text" | "image" | "quote" | "list";
  imageUrl?: string;
  listItems?: string[];
}

export interface BlogDetailsProps {
  post?: BlogDetailPost;
  onShare?: (platform: string, postId: number) => void;
  onRelatedPostClick?: (postId: number) => void;
}

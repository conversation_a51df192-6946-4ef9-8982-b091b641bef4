import Navbar from "@/components/shared/Navbar";
import clsx from "clsx";
import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Footer from "@/components/shared/Footer";

const lora = Lora({
  variable: "--font-lora",
  subsets: ["latin"],
  weight: ["400", "700"],
});

export const metadata: Metadata = {
  title: "RidhiSidhi Jewellers",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={clsx(`antialiased`, lora.variable, lora.className)}>
        <Navbar />
        {children}
        <Footer />
      </body>
    </html>
  );
}

"use client";

import React from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BlogPost } from "../types";

// Sample blog data - in a real app this would come from an API
const blogPosts: BlogPost[] = [
  {
    id: 1,
    title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
    excerpt:
      "Diamonds aren't just forever — they're evolving. From bold statement pieces to minimal everyday sparkles, 2025's...",
    image: "/placeholder.svg?height=200&width=300",
    category: "Diamond",
    date: "Dec 1, 2023",
    readTime: "5 min read",
  },
  {
    id: 2,
    title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
    excerpt:
      "Diamonds aren't just forever — they're evolving. From bold statement pieces to minimal everyday sparkles, 2025's...",
    image: "/placeholder.svg?height=200&width=300",
    category: "Diamond",
    date: "Dec 1, 2023",
    readTime: "5 min read",
  },
  {
    id: 3,
    title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
    excerpt:
      "Diamonds aren't just forever — they're evolving. From bold statement pieces to minimal everyday sparkles, 2025's...",
    image: "/placeholder.svg?height=200&width=300",
    category: "Diamond",
    date: "Dec 1, 2023",
    readTime: "5 min read",
  },
  {
    id: 4,
    title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
    excerpt:
      "Diamonds aren't just forever — they're evolving. From bold statement pieces to minimal everyday sparkles, 2025's...",
    image: "/placeholder.svg?height=200&width=300",
    category: "Diamond",
    date: "Dec 1, 2023",
    readTime: "5 min read",
  },
  {
    id: 5,
    title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
    excerpt:
      "Diamonds aren't just forever — they're evolving. From bold statement pieces to minimal everyday sparkles, 2025's...",
    image: "/placeholder.svg?height=200&width=300",
    category: "Diamond",
    date: "Dec 1, 2023",
    readTime: "5 min read",
  },
  {
    id: 6,
    title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
    excerpt:
      "Diamonds aren't just forever — they're evolving. From bold statement pieces to minimal everyday sparkles, 2025's...",
    image: "/placeholder.svg?height=200&width=300",
    category: "Diamond",
    date: "Dec 1, 2023",
    readTime: "5 min read",
  },
];

const BlogList = () => {
  return (
    <div>
      {/* Header Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Latest Blog Posts</h1>
        <p className="text-muted-foreground">
          Discover the latest trends and insights in diamond jewelry
        </p>
      </div>

      {/* Blog Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {blogPosts.map((post) => (
          <BlogCard key={post.id} post={post} />
        ))}
      </div>

      {/* Load More Section */}
      <div className="text-center mt-12">
        <Button
          className="bg-custom-red-500 text-white hover:bg-custom-red-600"
          size="lg"
        >
          Load More Posts
        </Button>
      </div>
    </div>
  );
};

// Blog Card Component
interface BlogCardProps {
  post: BlogPost;
}

export const BlogCard: React.FC<BlogCardProps> = ({ post }) => {
  return (
    <div className="overflow-hidden p-0 group">
      {/* Image Section */}
      <div className="relative overflow-hidden">
        <Image
          src={post.image}
          alt={post.title}
          width={300}
          height={200}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <Badge
            variant="secondary"
            className="bg-custom-blue-500 text-white hover:bg-custom-blue-600"
          >
            {post.category}
          </Badge>
        </div>
      </div>

      {/* Content Section */}
      <div>
        {/* Date and Read Time */}
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
          <span>{post.date}</span>
          {post.readTime && <span>{post.readTime}</span>}
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold mb-3 line-clamp-2 group-hover:text-custom-blue-500 transition-colors">
          {post.title}
        </h3>

        {/* Excerpt */}
        <p className="text-muted-foreground text-justify text-sm mb-4 line-clamp-3">
          {post.excerpt}
        </p>

        {/* Read More Button */}
        <Button
          variant="link"
          className="p-0 h-auto text-custom-red-500 hover:text-custom-red-600 font-medium"
        >
          Read More →
        </Button>
      </div>
    </div>
  );
};

export default BlogList;

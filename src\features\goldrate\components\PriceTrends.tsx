// "use client";
// import React from "react";
// import dynamic from "next/dynamic";
// import { ApexOptions } from "apexcharts";

// // Dynamically import ApexCharts to prevent SSR issues
// const ReactApexChart = dynamic(() => import("react-apexcharts"), {
//   ssr: false,
// });

// interface ChartProps {
//   title: string;
//   categories: string[];
//   series: { name: string; data: number[] }[];
//   height?: number;
// }

// const PriceTrendChart: React.FC<ChartProps> = ({
//   title,
//   categories,
//   series,
//   height = 350,
// }) => {
//   const chartOptions: ApexOptions = {
//     chart: {
//       type: "line",
//       height: height,
//       toolbar: {
//         show: false,
//       },
//       background: "#FFF", // Match white background from image
//     },
//     title: {
//       text: title,
//       align: "left",
//       style: {
//         fontSize: "16px",
//         fontWeight: "600",
//         color: "#333",
//       },
//     },
//     xaxis: {
//       categories: categories,
//       labels: {
//         style: {
//           fontSize: "12px",
//           colors: "#666",
//         },
//       },
//       axisBorder: {
//         show: false,
//       },
//     },
//     yaxis: {
//       title: {
//         text: "Cost of Assets",
//         style: {
//           fontSize: "12px",
//           color: "#666",
//         },
//       },
//       min: 0,
//       max: 100,
//       labels: {
//         style: {
//           fontSize: "12px",
//           colors: "#666",
//         },
//         formatter: (value) => `${value}`,
//       },
//     },
//     stroke: {
//       curve: "smooth" as const,
//       width: 2,
//     },
//     colors: ["#8B5CF6"], // Purple line from image (Tailwind purple-500)
//     grid: {
//       borderColor: "#e0e0e0",
//       row: {
//         colors: ["#f5f5f5", "transparent"], // Light gray grid lines
//         opacity: 0.5,
//       },
//       padding: {
//         top: 0,
//         right: 20,
//         bottom: 0,
//         left: 20,
//       },
//     },
//     tooltip: {
//       theme: "dark",
//       x: {
//         format: "yyyy",
//       },
//       y: {
//         formatter: (value) => `${value}`,
//       },
//     },
//     responsive: [
//       {
//         breakpoint: 640,
//         options: {
//           chart: {
//             height: 250,
//           },
//           title: {
//             style: {
//               fontSize: "14px",
//             },
//           },
//         },
//       },
//     ],
//   };

//   return (
//     <div className="w-full p-4 bg-white rounded-lg shadow-md border border-gray-200">
//       <div className="flex justify-between items-center mb-4">
//         <h3 className="text-lg font-semibold text-gray-700">Price Trends</h3>
//         <div className="space-x-2">
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             Last 7 days
//           </button>
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             30 days
//           </button>
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             3 months
//           </button>
//         </div>
//       </div>
//       <ReactApexChart
//         options={chartOptions}
//         series={series}
//         type="line"
//         height={height}
//       />
//     </div>
//   );
// };

// export default PriceTrendChart;

// Second

// "use client";
// import React from "react";
// import dynamic from "next/dynamic";
// import { ApexOptions } from "apexcharts";

// // Dynamically import ApexCharts to prevent SSR issues
// const ReactApexChart = dynamic(() => import("react-apexcharts"), {
//   ssr: false,
// });

// interface ChartProps {
//   title: string;
//   categories: string[];
//   series: { name: string; data: number[] }[];
//   height?: number;
// }

// const PriceTrendChart: React.FC<ChartProps> = ({
//   title,
//   categories,
//   series,
//   height = 350,
// }) => {
//   // Calculate the maximum value from series data with a buffer
//   const maxDataValue = Math.max(...series.flatMap((s) => s.data));
//   const yAxisMax = maxDataValue * 1.1; // Add 10% buffer

//   const chartOptions: ApexOptions = {
//     chart: {
//       type: "line",
//       height: height,
//       toolbar: {
//         show: false,
//       },
//       background: "#FFF",
//     },
//     title: {
//       text: title,
//       align: "left",
//       style: {
//         fontSize: "16px",
//         fontWeight: "600",
//         color: "#333",
//       },
//     },
//     xaxis: {
//       categories: categories,
//       labels: {
//         style: {
//           fontSize: "12px",
//           colors: "#666",
//         },
//       },
//       axisBorder: {
//         show: false,
//       },
//     },
//     yaxis: {
//       title: {
//         text: "Cost of Assets",
//         style: {
//           fontSize: "12px",
//           color: "#666",
//         },
//       },
//       min: 0,
//       max: yAxisMax, // Dynamically set max based on data
//       labels: {
//         style: {
//           fontSize: "12px",
//           colors: "#666",
//         },
//         formatter: (value) => `${value}`,
//       },
//     },
//     stroke: {
//       curve: "smooth" as const,
//       width: 2,
//     },
//     colors: ["#8B5CF6"],
//     grid: {
//       borderColor: "#e0e0e0",
//       row: {
//         colors: ["#f5f5f5", "transparent"],
//         opacity: 0.5,
//       },
//       padding: {
//         top: 0,
//         right: 20,
//         bottom: 0,
//         left: 20,
//       },
//     },
//     tooltip: {
//       theme: "dark",
//       x: {
//         format: "yyyy",
//       },
//       y: {
//         formatter: (value) => `${value}`,
//       },
//     },
//     responsive: [
//       {
//         breakpoint: 640,
//         options: {
//           chart: {
//             height: 250,
//           },
//           title: {
//             style: {
//               fontSize: "14px",
//             },
//           },
//         },
//       },
//     ],
//   };

//   return (
//     <div className="w-full p-4 bg-white rounded-lg shadow-md border border-gray-200">
//       <div className="flex justify-between items-center mb-4">
//         <h3 className="text-lg font-semibold text-gray-700">Price Trends</h3>
//         <div className="space-x-2">
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             Last 7 days
//           </button>
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             30 days
//           </button>
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             3 months
//           </button>
//         </div>
//       </div>
//       <ReactApexChart
//         options={chartOptions}
//         series={series}
//         type="line"
//         height={height}
//       />
//     </div>
//   );
// };

// export default PriceTrendChart;

// "use client";
// import React from "react";
// import dynamic from "next/dynamic";
// import { ApexOptions } from "apexcharts";

// // Dynamically import ApexCharts to prevent SSR issues
// const ReactApexChart = dynamic(() => import("react-apexcharts"), {
//   ssr: false,
// });

// interface ChartProps {
//   title: string;
//   categories: string[];
//   series: { name: string; data: number[] }[];
//   height?: number;
// }

// const PriceTrendChart: React.FC<ChartProps> = ({
//   title,
//   categories,
//   series,
//   height = 350,
// }) => {
//   // Calculate the maximum value from series data with a buffer
//   const maxDataValue = Math.max(...series.flatMap((s) => s.data));
//   const yAxisMax = maxDataValue * 1.1; // Add 10% buffer

//   const chartOptions: ApexOptions = {
//     chart: {
//       type: "line",
//       height: height,
//       toolbar: {
//         show: false,
//       },
//       background: "#FFF",
//     },
//     title: {
//       text: title,
//       align: "left",
//       style: {
//         fontSize: "16px",
//         fontWeight: "600",
//         color: "#333",
//       },
//     },
//     xaxis: {
//       categories: categories,
//       labels: {
//         style: {
//           fontSize: "12px",
//           colors: "#666",
//         },
//       },
//       axisBorder: {
//         show: false,
//       },
//     },
//     yaxis: {
//       title: {
//         text: "Cost of Assets",
//         style: {
//           fontSize: "12px",
//           color: "#666",
//         },
//       },
//       min: 0,
//       max: 100, // Fixed range 0 to 100
//       labels: {
//         style: {
//           fontSize: "12px",
//           colors: "#666",
//         },
//         formatter: (value) => `${value}`,
//       },
//     },
//     stroke: {
//       curve: "smooth" as const,
//       width: 2,
//     },
//     colors: ["#8B5CF6"],
//     grid: {
//       borderColor: "#e0e0e0",
//       row: {
//         colors: ["#f5f5f5", "transparent"],
//         opacity: 0.5,
//       },
//       padding: {
//         top: 0,
//         right: 20,
//         bottom: 0,
//         left: 20,
//       },
//     },
//     tooltip: {
//       theme: "dark",
//       x: {
//         format: "yyyy",
//       },
//       y: {
//         formatter: (value) => `${value}`,
//       },
//     },
//     responsive: [
//       {
//         breakpoint: 640,
//         options: {
//           chart: {
//             height: 250,
//           },
//           title: {
//             style: {
//               fontSize: "14px",
//             },
//           },
//         },
//       },
//     ],
//   };

//   return (
//     <div className="w-full p-4 bg-white rounded-lg shadow-md border border-gray-200">
//       <div className="flex justify-between items-center mb-4">
//         <h3 className="text-lg font-semibold text-gray-700">Price Trends</h3>
//         <div className="space-x-2">
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             Last 7 days
//           </button>
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             30 days
//           </button>
//           <button className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600">
//             3 months
//           </button>
//         </div>
//       </div>
//       <ReactApexChart
//         options={chartOptions}
//         series={series}
//         type="line"
//         height={height}
//       />
//     </div>
//   );
// };

// export default PriceTrendChart;

"use client";
import React, { useState } from "react";
import dynamic from "next/dynamic";
import { ApexOptions } from "apexcharts";

// Dynamically import ApexCharts to prevent SSR issues
const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

interface ChartProps {
  title: string;
  categories: string[];
  series: { name: string; data: number[] }[];
  height?: number;
}

const PriceTrendChart: React.FC<ChartProps> = ({
  title,
  categories: initialCategories,
  series: initialSeries,
  height = 350,
}) => {
  const [selectedRange, setSelectedRange] = useState("11 years"); // Default to all years

  // Define yearsMap for filtering based on number of years
  const yearsMap: { [key: string]: number } = {
    "Last 7 days": 1, // Last 1 year
    "30 days": 3, // Last 3 years
    "3 months": 11, // All 11 years (2015-2025)
  };

  // Filter data based on selected range (number of years)
  const getFilteredData = () => {
    const currentYear = new Date().getFullYear(); // 2025
    const yearsToShow = yearsMap[selectedRange as keyof typeof yearsMap];

    const filteredCategories = initialCategories
      .filter((cat) => {
        const catYear = parseInt(cat, 10);
        if (isNaN(catYear)) return false; // Skip invalid years
        const yearDiff = currentYear - catYear;
        return yearDiff >= 0 && yearDiff < yearsToShow; // Include years from current year back to N years
      })
      .slice(-yearsToShow); // Take the last N years to ensure correct range

    // If no categories are filtered or range exceeds data, return all data
    if (
      filteredCategories.length === 0 ||
      yearsToShow >= initialCategories.length
    ) {
      return {
        categories: initialCategories,
        series: initialSeries,
      };
    }

    const filteredData = initialSeries[0].data.slice(
      -filteredCategories.length
    );

    return {
      categories: filteredCategories,
      series: [{ name: initialSeries[0].name, data: filteredData }],
    };
  };

  const { categories, series } = getFilteredData();

  const chartOptions: ApexOptions = {
    chart: {
      type: "line",
      height: height,
      toolbar: {
        show: false,
      },
      background: "#FFF",
    },
    title: {
      text: title,
      align: "left",
      style: {
        fontSize: "16px",
        fontWeight: "600",
        color: "#333",
      },
    },
    xaxis: {
      categories: categories,
      labels: {
        style: {
          fontSize: "12px",
          colors: "#666",
        },
      },
      axisBorder: {
        show: false,
      },
    },
    yaxis: {
      title: {
        text: "Cost of Assets",
        style: {
          fontSize: "12px",
          color: "#666",
        },
      },
      min: 0,
      max: 100, // Fixed range 0 to 100
      labels: {
        style: {
          fontSize: "12px",
          colors: "#666",
        },
        formatter: (value) => `${value}`,
      },
    },
    stroke: {
      curve: "smooth" as const,
      width: 2,
    },
    colors: ["#8B5CF6"],
    grid: {
      borderColor: "#e0e0e0",
      row: {
        colors: ["#f5f5f5", "transparent"],
        opacity: 0.5,
      },
      padding: {
        top: 0,
        right: 20,
        bottom: 0,
        left: 20,
      },
    },
    tooltip: {
      theme: "dark",
      x: {
        format: "yyyy",
      },
      y: {
        formatter: (value) => `${value}`,
      },
    },
    responsive: [
      {
        breakpoint: 640,
        options: {
          chart: {
            height: 250,
          },
          title: {
            style: {
              fontSize: "14px",
            },
          },
        },
      },
    ],
  };

  return (
    <div className="w-full p-4 bg-white rounded-lg shadow-md border border-gray-200">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-700 font-lora">
          Price Trends
        </h3>
        <div className="space-x-2 font-lora">
          <button
            className="px-3 py-1 bg-custom-blue-500 text-white rounded-lg cursor-pointer hover:bg-custom-blue-600"
            onClick={() => setSelectedRange("Last 7 days")}
          >
            Last 7 days
          </button>
          <button
            className="px-3 py-1 bg-custom-blue-500 text-white rounded-lg cursor-pointer hover:bg-custom-blue-600"
            onClick={() => setSelectedRange("30 days")}
          >
            30 days
          </button>
          <button
            className="px-3 py-1 bg-custom-blue-500 text-white rounded-lg cursor-pointer hover:bg-custom-blue-600"
            onClick={() => setSelectedRange("3 months")}
          >
            3 months
          </button>
        </div>
      </div>
      <ReactApexChart
        options={chartOptions}
        series={series}
        type="line"
        height={height}
      />
    </div>
  );
};

export default PriceTrendChart;

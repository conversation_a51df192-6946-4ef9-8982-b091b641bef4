import Image from "next/image";
import React from "react";

const obj = [
  { icon: "/svg/exchange.svg", text: "15 Day Exchange" },
  { icon: "/svg/certification.svg", text: "100% Certified" },
  { icon: "/svg/verified.svg", text: "1 Year Warranty" },
  {
    icon: "/svg/life.svg",
    text: "Lifetime Exchange",
  },
];
const Promise = () => {
  return (
    <div className="w-full  bg-custom-violet-50 my-4 py-4">
      <div className="w-fit mx-auto flex items-center gap-4">
        {obj.map((item, index) => (
          <div key={index.toString()} className="flex items-center gap-2">
            <Image
              className=""
              width={24}
              height={24}
              src={item.icon}
              alt={item.text}
            />
            <p>{item.text}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Promise;

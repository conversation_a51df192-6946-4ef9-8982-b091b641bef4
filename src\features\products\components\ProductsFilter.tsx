"use client";

import { useState } from "react";
import { useForm, Controller } from "react-hook-form";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronUp } from "lucide-react";
import FilterHeader from "./FilterHeader";
import { Card, CardContent } from "@/components/ui/card";

interface FilterFormData {
  material: string[];
  price: string[];
  productType: string[];
  occasion: string[];
  carat: string[];
  weightRange: string[];
  ringSize: string[];
  ringStyle: string[];
}

export default function ProductsFilter() {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({
    price: false,
    productType: false,
    occasion: false,
    weightRange: false,
    ringSize: false,
    ringStyle: false,
  });

  const { control, watch, setValue, reset } = useForm<FilterFormData>({
    defaultValues: {
      material: ["gold"],
      price: [],
      productType: [],
      occasion: [],
      carat: [],
      weightRange: [],
      ringSize: [],
      ringStyle: [],
    },
  });

  const watchedValues = watch();

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleClearAll = () => {
    reset({
      material: [],
      price: [],
      productType: [],
      occasion: [],
      carat: [],
      weightRange: [],
      ringSize: [],
      ringStyle: [],
    });
  };

  const getTotalSelectedFilters = () => {
    return Object.values(watchedValues).flat().length;
  };

  return (
    <Card className="w-72 rounded-sm">
      <CardContent>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-medium text-gray-700">Filters</h2>
            <span className="bg-gray-100 text-gray-600 text-sm px-2 py-1 rounded">
              {getTotalSelectedFilters()}
            </span>
          </div>
          <button
            onClick={handleClearAll}
            className="text-purple-600 text-sm font-medium hover:text-purple-700"
          >
            Clear All
          </button>
        </div>

        {/* Material Section */}
        <div className="mb-6">
          <FilterHeader title="Material" />
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Controller
                name="material"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="gold"
                    checked={field.value.includes("gold")}
                    onCheckedChange={(checked) => {
                      const currentValues = [...field.value];
                      if (checked) {
                        if (!currentValues.includes("gold")) {
                          field.onChange([...currentValues, "gold"]);
                        }
                      } else {
                        field.onChange(
                          currentValues.filter((value) => value !== "gold")
                        );
                      }
                    }}
                  />
                )}
              />
              <label
                htmlFor="gold"
                className="text-sm text-custom-red-500 cursor-pointer"
              >
                Gold (721)
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Controller
                name="material"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="diamond"
                    checked={field.value.includes("diamond")}
                    onCheckedChange={(checked) => {
                      const currentValues = [...field.value];
                      if (checked) {
                        if (!currentValues.includes("diamond")) {
                          field.onChange([...currentValues, "diamond"]);
                        }
                      } else {
                        field.onChange(
                          currentValues.filter((value) => value !== "diamond")
                        );
                      }
                    }}
                  />
                )}
              />
              <label
                htmlFor="diamond"
                className="text-sm text-custom-red-500 cursor-pointer"
              >
                Diamond (721)
              </label>
            </div>
          </div>
        </div>

        {/* Price Section */}
        <div className="mb-6">
          <FilterHeader title="Price" />
          <div className="space-y-3">
            {[
              { id: "under-8000", label: "Under NPR 8,000 (721)" },
              { id: "8000-16000", label: "NPR 8,001 - NPR 16,000 (721)" },
              { id: "16000-24000", label: "NPR 16,001 - NPR 24,000 (721)" },
              { id: "24000-32000", label: "NPR 24,001 - NPR 32,000 (721)" },
            ].map((priceRange) => (
              <div key={priceRange.id} className="flex items-center space-x-2">
                <Controller
                  name="price"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={priceRange.id}
                      checked={field.value.includes(priceRange.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(priceRange.id)) {
                            field.onChange([...currentValues, priceRange.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter(
                              (value) => value !== priceRange.id
                            )
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={priceRange.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {priceRange.label}
                </label>
              </div>
            ))}
            <button
              onClick={() => toggleSection("price")}
              className="flex items-center gap-1 text-purple-600 text-sm font-medium hover:text-purple-700"
            >
              {expandedSections.price ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              View 6 More
            </button>
          </div>
        </div>

        {/* Product Type Section */}
        <div className="mb-6">
          <FilterHeader title="Product Type" />
          <div className="space-y-3">
            {[
              { id: "earrings", label: "Earrings (721)" },
              { id: "rings", label: "Rings (721)" },
              { id: "necklaces", label: "Necklaces (721)" },
              { id: "bracelets", label: "Bracelets (721)" },
            ].map((productType) => (
              <div key={productType.id} className="flex items-center space-x-2">
                <Controller
                  name="productType"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={productType.id}
                      checked={field.value.includes(productType.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(productType.id)) {
                            field.onChange([...currentValues, productType.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter(
                              (value) => value !== productType.id
                            )
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={productType.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {productType.label}
                </label>
              </div>
            ))}
            <button
              onClick={() => toggleSection("productType")}
              className="flex items-center gap-1 text-purple-600 text-sm font-medium hover:text-purple-700"
            >
              {expandedSections.productType ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              View 6 More
            </button>
          </div>
        </div>

        {/* Occasion Section */}
        <div className="mb-6">
          <FilterHeader title="Occasion" />
          <div className="space-y-3">
            {[
              { id: "work-wear", label: "Work Wear (721)" },
              { id: "anniversary", label: "Anniversary (721)" },
              { id: "wedding", label: "Wedding (721)" },
              { id: "birthday", label: "Birthday (721)" },
            ].map((occasion) => (
              <div key={occasion.id} className="flex items-center space-x-2">
                <Controller
                  name="occasion"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={occasion.id}
                      checked={field.value.includes(occasion.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(occasion.id)) {
                            field.onChange([...currentValues, occasion.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter(
                              (value) => value !== occasion.id
                            )
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={occasion.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {occasion.label}
                </label>
              </div>
            ))}
            <button
              onClick={() => toggleSection("occasion")}
              className="flex items-center gap-1 text-purple-600 text-sm font-medium hover:text-purple-700"
            >
              {expandedSections.occasion ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              View 6 More
            </button>
          </div>
        </div>

        {/* Carat Section */}
        <div className="mb-6">
          <FilterHeader title="Carat" />
          <div className="space-y-3">
            {[
              { id: "24kt", label: "24 kt (721)" },
              { id: "22kt", label: "22 kt (721)" },
              { id: "18kt", label: "18 kt (721)" },
              { id: "14k", label: "14k (721)" },
            ].map((carat) => (
              <div key={carat.id} className="flex items-center space-x-2">
                <Controller
                  name="carat"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={carat.id}
                      checked={field.value.includes(carat.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(carat.id)) {
                            field.onChange([...currentValues, carat.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter((value) => value !== carat.id)
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={carat.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {carat.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Weight Range Section */}
        <div className="mb-6">
          <FilterHeader title="Weight Range" />
          <div className="space-y-3">
            {[
              { id: "0-2g", label: "0-2 g (721)" },
              { id: "2-5g", label: "2-5 g (721)" },
              { id: "5-10g", label: "5-10 g (721)" },
              { id: "10-20g", label: "10-20 g (721)" },
            ].map((weightRange) => (
              <div key={weightRange.id} className="flex items-center space-x-2">
                <Controller
                  name="weightRange"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={weightRange.id}
                      checked={field.value.includes(weightRange.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(weightRange.id)) {
                            field.onChange([...currentValues, weightRange.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter(
                              (value) => value !== weightRange.id
                            )
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={weightRange.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {weightRange.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Ring Size Section */}
        <div className="mb-6">
          <FilterHeader title="Ring Size" />
          <div className="space-y-3">
            {[
              { id: "size-5", label: "5 (12)" },
              { id: "size-6", label: "6 (13)" },
              { id: "size-7", label: "7 (10)" },
              { id: "size-8", label: "8 (4)" },
            ].map((ringSize) => (
              <div key={ringSize.id} className="flex items-center space-x-2">
                <Controller
                  name="ringSize"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={ringSize.id}
                      checked={field.value.includes(ringSize.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(ringSize.id)) {
                            field.onChange([...currentValues, ringSize.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter(
                              (value) => value !== ringSize.id
                            )
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={ringSize.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {ringSize.label}
                </label>
              </div>
            ))}
            <button
              onClick={() => toggleSection("ringSize")}
              className="flex items-center gap-1 text-purple-600 text-sm font-medium hover:text-purple-700"
            >
              {expandedSections.ringSize ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              View 9 More
            </button>
          </div>
        </div>

        {/* Ring Style Section */}
        <div className="mb-6">
          <FilterHeader title="Ring Style" subtitle="For Women" />
          <div className="space-y-3">
            {[
              { id: "plain-band", label: "Plain Band Ring (12)" },
              { id: "floral-design", label: "Floral Design Ring (13)" },
              { id: "mehendi-inspired", label: "Mehendi-inspired Ring (10)" },
              { id: "stone-embedded", label: "Stone-Embedded Ring (4)" },
            ].map((ringStyle) => (
              <div key={ringStyle.id} className="flex items-center space-x-2">
                <Controller
                  name="ringStyle"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      id={ringStyle.id}
                      checked={field.value.includes(ringStyle.id)}
                      onCheckedChange={(checked) => {
                        const currentValues = [...field.value];
                        if (checked) {
                          if (!currentValues.includes(ringStyle.id)) {
                            field.onChange([...currentValues, ringStyle.id]);
                          }
                        } else {
                          field.onChange(
                            currentValues.filter(
                              (value) => value !== ringStyle.id
                            )
                          );
                        }
                      }}
                    />
                  )}
                />
                <label
                  htmlFor={ringStyle.id}
                  className="text-sm text-custom-red-500 cursor-pointer"
                >
                  {ringStyle.label}
                </label>
              </div>
            ))}
            <button
              onClick={() => toggleSection("ringStyle")}
              className="flex items-center gap-1 text-purple-600 text-sm font-medium hover:text-purple-700"
            >
              {expandedSections.ringStyle ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
              View 8 More
            </button>
          </div>
        </div>
      </CardContent>
      {/* Header */}
    </Card>
  );
}

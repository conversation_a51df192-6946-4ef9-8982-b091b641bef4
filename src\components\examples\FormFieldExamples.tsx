"use client";

import React, { useState } from "react";
import { InputField, TextareaField } from "@/components/ui/form-field";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, User, Phone, Search, Lock } from "lucide-react";

export default function FormFieldExamples() {
  const [formData, setFormData] = useState({
    email: "",
    name: "",
    phone: "",
    password: "",
    confirmPassword: "",
    bio: "",
    search: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.name) {
      newErrors.name = "Name is required";
    } else if (formData.name.length < 2) {
      newErrors.name = "Name must be at least 2 characters long";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters long";
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    if (formData.bio && formData.bio.length > 500) {
      newErrors.bio = "Bio must be less than 500 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("Form submitted:", formData);
      alert("Form submitted successfully!");
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-custom-red-500 mb-2">
          Mantine-like Form Fields
        </h1>
        <p className="text-muted-foreground">
          Comprehensive form components with labels, validation, and accessibility
        </p>
      </div>

      {/* Basic Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Basic Form Fields</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <InputField
              label="Email Address"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleInputChange("email")}
              error={errors.email}
              required
              leftSection={<Mail className="h-4 w-4" />}
              description="We'll never share your email with anyone else"
            />

            <InputField
              label="Full Name"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={handleInputChange("name")}
              error={errors.name}
              withAsterisk
              leftSection={<User className="h-4 w-4" />}
            />

            <InputField
              label="Phone Number"
              type="tel"
              placeholder="+977 98XXXXXXXX"
              value={formData.phone}
              onChange={handleInputChange("phone")}
              leftSection={<Phone className="h-4 w-4" />}
              description="Optional: For order updates"
            />

            <InputField
              label="Search Products"
              placeholder="Search for jewelry..."
              value={formData.search}
              onChange={handleInputChange("search")}
              leftSection={<Search className="h-4 w-4" />}
              rightSection={
                <Button size="sm" variant="ghost" className="h-6 px-2">
                  Go
                </Button>
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Password Fields */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Password Fields</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <InputField
              label="Password"
              type="password"
              placeholder="Enter your password"
              value={formData.password}
              onChange={handleInputChange("password")}
              error={errors.password}
              required
              withPasswordToggle
              leftSection={<Lock className="h-4 w-4" />}
              description="Must be at least 8 characters long"
            />

            <InputField
              label="Confirm Password"
              type="password"
              placeholder="Confirm your password"
              value={formData.confirmPassword}
              onChange={handleInputChange("confirmPassword")}
              error={errors.confirmPassword}
              required
              withPasswordToggle
              leftSection={<Lock className="h-4 w-4" />}
            />
          </div>
        </CardContent>
      </Card>

      {/* Textarea Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Textarea Fields</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <TextareaField
            label="Bio"
            placeholder="Tell us about yourself..."
            value={formData.bio}
            onChange={handleInputChange("bio")}
            error={errors.bio}
            description={`${formData.bio.length}/500 characters`}
            rows={4}
            resize="vertical"
          />

          <div className="grid md:grid-cols-2 gap-6">
            <TextareaField
              label="Small Textarea"
              placeholder="Small size example"
              size="sm"
              rows={3}
              resize="none"
            />

            <TextareaField
              label="Large Textarea"
              placeholder="Large size example"
              size="lg"
              rows={3}
              resize="both"
            />
          </div>
        </CardContent>
      </Card>

      {/* Size Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Size Variants</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <InputField
              label="Small Input"
              placeholder="Small size input"
              size="sm"
              leftSection={<User className="h-3 w-3" />}
            />

            <InputField
              label="Medium Input (Default)"
              placeholder="Medium size input"
              size="md"
              leftSection={<User className="h-4 w-4" />}
            />

            <InputField
              label="Large Input"
              placeholder="Large size input"
              size="lg"
              leftSection={<User className="h-5 w-5" />}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Submission */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Complete Form Example</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <InputField
                label="Email"
                type="email"
                value={formData.email}
                onChange={handleInputChange("email")}
                error={errors.email}
                required
                leftSection={<Mail className="h-4 w-4" />}
              />

              <InputField
                label="Name"
                value={formData.name}
                onChange={handleInputChange("name")}
                error={errors.name}
                required
                leftSection={<User className="h-4 w-4" />}
              />
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <InputField
                label="Password"
                type="password"
                value={formData.password}
                onChange={handleInputChange("password")}
                error={errors.password}
                required
                withPasswordToggle
                leftSection={<Lock className="h-4 w-4" />}
              />

              <InputField
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={handleInputChange("confirmPassword")}
                error={errors.confirmPassword}
                required
                withPasswordToggle
                leftSection={<Lock className="h-4 w-4" />}
              />
            </div>

            <TextareaField
              label="Bio"
              value={formData.bio}
              onChange={handleInputChange("bio")}
              error={errors.bio}
              placeholder="Tell us about yourself..."
              description={`${formData.bio.length}/500 characters`}
              rows={4}
            />

            <div className="flex gap-4">
              <Button type="submit" className="bg-custom-blue-500 hover:bg-custom-blue-600">
                Submit Form
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setFormData({
                    email: "",
                    name: "",
                    phone: "",
                    password: "",
                    confirmPassword: "",
                    bio: "",
                    search: "",
                  });
                  setErrors({});
                }}
              >
                Reset
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

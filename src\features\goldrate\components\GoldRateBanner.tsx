// import Link from "next/link";
// import React from "react";

// interface BannerProps {
//   imageSrc: string;
//   title: string;
//   topTitle: string;
//   buttonText: string;
//   description?: string;
//   to: string;
// }

// const GoldRateBanner: React.FC<BannerProps> = ({
//   imageSrc,
//   title,
//   topTitle,
//   buttonText,
//   description,
//   to,
// }) => {
//   return (
//     <div
//       className="relative w-full h-64 bg-cover bg-center rounded-xl"
//       style={{ backgroundImage: `url(${imageSrc})` }}
//     >
//       <div className="relative flex items-center justify-end h-full ">
//         <div className="text-start font-lora space-y-2 max-w-md pr-4">
//           <p className="font-semibold text-custom-textgray text-sm lg:text-base">
//             {topTitle}
//           </p>
//           <h2 className="text-custom-red-500 text-sm md:text-base lg:text-lg font-bold mt-2">
//             {title}
//           </h2>
//           {description && (
//             <p className="text-xs text-custom-textgray max-w-xs">
//               {description}
//             </p>
//           )}
//           <div className="mt-4">
//             <Link
//               href={to}
//               className="inline-block px-6 md:px-8 lg:px-10 py-2 border-custom-red-500 border-2 hover:bg-opacity-30 text-custom-red-500 rounded cursor-pointer text-sm font-semibold hover:text-white hover:bg-custom-red-500"
//             >
//               {buttonText}
//             </Link>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default GoldRateBanner;

import Link from "next/link";
import React from "react";

interface BannerProps {
  imageSrc: string;
  title: string;
  topTitle: string;
  buttonText: string;
  description?: string;
  to: string;
}

const GoldRateBanner: React.FC<BannerProps> = ({
  imageSrc,
  title,
  topTitle,
  buttonText,
  description,
  to,
}) => {
  return (
    <div
      className="relative w-full h-64 bg-cover bg-center rounded-xl"
      style={{ backgroundImage: `url(${imageSrc})` }}
    >
      <div className="relative flex items-center h-full w-full justify-end">
        {/* Small screen layout */}
        <div className="flex items-center justify-center w-full h-full text-center sm:hidden">
          <div className="space-y-2">
            <p className="font-semibold text-custom-textgray text-sm lg:text-base mb-2">
              {topTitle}
            </p>
            <h2 className="text-custom-red-500 text-sm md:text-base lg:text-lg font-bold mt-0">
              {title}
            </h2>
            {description && (
              <p className="text-xs text-custom-textgray max-w-xs mt-2">
                {description}
              </p>
            )}
            <div className="mt-4">
              <Link
                href={to}
                className="inline-block px-6 md:px-8 lg:px-10 py-2 border-custom-red-500 border-2 hover:bg-opacity-30 text-custom-red-500 rounded cursor-pointer text-sm font-semibold hover:text-white hover:bg-custom-red-500"
              >
                {buttonText}
              </Link>
            </div>
          </div>
        </div>
        {/* Large screen layout */}
        <div className="hidden sm:flex items-center justify-end w-full h-full pr-4">
          <div className="text-start font-lora space-y-2 max-w-md">
            <p className="font-semibold text-custom-textgray text-sm lg:text-base">
              {topTitle}
            </p>
            <h2 className="text-custom-red-500 text-sm md:text-base lg:text-lg font-bold mt-2">
              {title}
            </h2>
            {description && (
              <p className="text-xs text-custom-textgray max-w-xs">
                {description}
              </p>
            )}
            <div className="mt-4">
              <Link
                href={to}
                className="inline-block px-6 md:px-8 lg:px-10 py-2 border-custom-red-500 border-2 hover:bg-opacity-30 text-custom-red-500 rounded cursor-pointer text-sm font-semibold hover:text-white hover:bg-custom-red-500"
              >
                {buttonText}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GoldRateBanner;

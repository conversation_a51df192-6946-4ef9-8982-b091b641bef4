import Image from "next/image";
import React from "react";

const GoldRateCard = () => {
  return (
    <div className="p-4 shadow-md border border-gray-200 rounded-2xl flex flex-col md:flex-row items-center gap-4 ">
      <Image
        src="/goldratepage/GoldRate.png"
        alt="Gold Rate image"
        height={200}
        width={200}
      />

      <div className="flex flex-col gap-2 font-lora">
        <h2 className=" text-base lg:text-[18px] font-semibold">
          Gold Rate Today!
        </h2>
        <p className="font-semibold text-sm md:text-sm lg:text-base text-custom-textgray">
          <span className="text-custom-red-500 font-semibold text-xl md:text-2xl lg:text-[28px]">
            NPR 9175
          </span>
          /Gram(22ct)
        </p>
        <p className="text-custom-textgray text-xs lg:text-sm">
          *Price may vary by city
        </p>
      </div>
    </div>
  );
};

export default GoldRateCard;

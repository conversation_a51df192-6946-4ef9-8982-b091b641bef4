"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, EyeOff } from "lucide-react";

// Base form field props
interface BaseFormFieldProps {
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  labelClassName?: string;
  descriptionClassName?: string;
  errorClassName?: string;
  withAsterisk?: boolean; // Alternative to required for visual indicator
  size?: "sm" | "md" | "lg";
}

// Input field specific props
interface InputFieldProps extends BaseFormFieldProps {
  type?: React.HTMLInputTypeAttribute;
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  leftSection?: React.ReactNode;
  rightSection?: React.ReactNode;
  withPasswordToggle?: boolean;
  inputClassName?: string;
  id?: string;
  name?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: string;
}

// Textarea field specific props
interface TextareaFieldProps extends BaseFormFieldProps {
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  rows?: number;
  cols?: number;
  resize?: "none" | "both" | "horizontal" | "vertical";
  textareaClassName?: string;
  id?: string;
  name?: string;
  autoFocus?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  minLength?: number;
}

// Size variants
const sizeVariants = {
  sm: {
    input: "h-8 px-2.5 py-1.5 text-sm",
    textarea: "px-2.5 py-1.5 text-sm min-h-12",
    label: "text-sm",
    description: "text-xs",
    error: "text-xs",
  },
  md: {
    input: "h-9 px-3 py-2 text-sm",
    textarea: "px-3 py-2 text-sm min-h-16",
    label: "text-sm",
    description: "text-xs",
    error: "text-xs",
  },
  lg: {
    input: "h-10 px-4 py-2.5 text-base",
    textarea: "px-4 py-2.5 text-base min-h-20",
    label: "text-base",
    description: "text-sm",
    error: "text-sm",
  },
};

// Input Field Component
const InputField = React.forwardRef<HTMLInputElement, InputFieldProps>(
  (
    {
      label,
      description,
      error,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      inputClassName,
      withAsterisk,
      size = "md",
      type = "text",
      leftSection,
      rightSection,
      withPasswordToggle = false,
      id,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [internalType, setInternalType] = React.useState(type);
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const errorId = error ? `${fieldId}-error` : undefined;

    // Handle password toggle
    React.useEffect(() => {
      if (withPasswordToggle && type === "password") {
        setInternalType(showPassword ? "text" : "password");
      } else {
        setInternalType(type);
      }
    }, [showPassword, type, withPasswordToggle]);

    const togglePassword = () => {
      setShowPassword(!showPassword);
    };

    const hasError = Boolean(error);
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];

    // Determine right section content
    const getRightSection = () => {
      if (withPasswordToggle && type === "password") {
        return (
          <button
            type="button"
            onClick={togglePassword}
            className="text-muted-foreground hover:text-foreground transition-colors p-1"
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        );
      }
      return rightSection;
    };

    const finalRightSection = getRightSection();

    return (
      <div className={cn("space-y-2", className)}>
        {/* Label */}
        {label && (
          <Label
            htmlFor={fieldId}
            className={cn(
              sizes.label,
              "font-medium",
              hasError && "text-custom-red-500",
              disabled && "opacity-50",
              labelClassName
            )}
          >
            {label}
            {isRequired && (
              <span className="text-custom-red-500 ml-1" aria-label="required">
                *
              </span>
            )}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Section */}
          {leftSection && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none">
              {leftSection}
            </div>
          )}

          {/* Input */}
          <Input
            ref={ref}
            id={fieldId}
            type={internalType}
            disabled={disabled}
            required={required}
            aria-invalid={hasError}
            aria-describedby={cn(
              descriptionId && descriptionId,
              errorId && errorId
            )}
            className={cn(
              sizes.input,
              leftSection && "pl-10",
              finalRightSection && "pr-10",
              hasError &&
                "border-custom-red-500 focus-visible:border-custom-red-500 focus-visible:ring-custom-red-500/20",
              inputClassName
            )}
            {...props}
          />

          {/* Right Section */}
          {finalRightSection && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {finalRightSection}
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div
            id={errorId}
            className={cn(
              sizes.error,
              "text-custom-red-500 flex items-center gap-1",
              errorClassName
            )}
            role="alert"
            aria-live="polite"
          >
            <AlertCircle className="h-3 w-3 flex-shrink-0" />
            {error}
          </div>
        )}
      </div>
    );
  }
);

InputField.displayName = "InputField";

// Textarea Field Component
const TextareaField = React.forwardRef<HTMLTextAreaElement, TextareaFieldProps>(
  (
    {
      label,
      description,
      error,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      textareaClassName,
      withAsterisk,
      size = "md",
      resize = "vertical",
      id,
      ...props
    },
    ref
  ) => {
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const errorId = error ? `${fieldId}-error` : undefined;

    const hasError = Boolean(error);
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];

    // Resize class mapping
    const resizeClasses = {
      none: "resize-none",
      both: "resize",
      horizontal: "resize-x",
      vertical: "resize-y",
    };

    return (
      <div className={cn("space-y-2", className)}>
        {/* Label */}
        {label && (
          <Label
            htmlFor={fieldId}
            className={cn(
              sizes.label,
              "font-medium",
              hasError && "text-custom-red-500",
              disabled && "opacity-50",
              labelClassName
            )}
          >
            {label}
            {isRequired && (
              <span className="text-custom-red-500 ml-1" aria-label="required">
                *
              </span>
            )}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Textarea */}
        <Textarea
          ref={ref}
          id={fieldId}
          disabled={disabled}
          required={required}
          aria-invalid={hasError}
          aria-describedby={cn(
            descriptionId && descriptionId,
            errorId && errorId
          )}
          className={cn(
            sizes.textarea,
            resizeClasses[resize],
            hasError &&
              "border-custom-red-500 focus-visible:border-custom-red-500 focus-visible:ring-custom-red-500/20",
            textareaClassName
          )}
          {...props}
        />

        {/* Error Message */}
        {error && (
          <div
            id={errorId}
            className={cn(
              sizes.error,
              "text-custom-red-500 flex items-center gap-1",
              errorClassName
            )}
            role="alert"
            aria-live="polite"
          >
            <AlertCircle className="h-3 w-3 flex-shrink-0" />
            {error}
          </div>
        )}
      </div>
    );
  }
);

TextareaField.displayName = "TextareaField";

export {
  InputField,
  TextareaField,
  type InputFieldProps,
  type TextareaFieldProps,
};

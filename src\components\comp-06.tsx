import { useId } from "react"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function Component() {
  const id = useId()
  return (
    <div className="*:not-first:mt-2">
      <Label htmlFor={id}>Input with error</Label>
      <Input
        id={id}
        className="peer"
        placeholder="Email"
        type="email"
        defaultValue="<EMAIL>"
        aria-invalid
      />
      <p
        className="peer-aria-invalid:text-destructive mt-2 text-xs"
        role="alert"
        aria-live="polite"
      >
        Email is invalid
      </p>
    </div>
  )
}

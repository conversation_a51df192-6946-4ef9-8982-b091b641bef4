import React from "react";
import StepCard from "./StepCard";
import Image from "next/image";
import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";

const HeroSection = () => {
  const stepCardData = [
    {
      stepText: "Step 1",
      description:
        "Bring your old gold jewellery to our store or start an exchange request online",
      imageSrc: "/goldexchangepage/image1.png",
    },
    {
      stepText: "Step 2",
      description:
        "Our experts will carefully assess the purity and weight of your gold using certified testing methods.",
      imageSrc: "/goldexchangepage/image2.png",
    },
    {
      stepText: "Step 3",
      description:
        "Get an instant and fully transparent quote based on today's gold rates.",
      imageSrc: "/goldexchangepage/image3.png",
    },
    {
      stepText: "Step 4",
      description:
        "Choose your new jewellery and pay only the difference - or take home something that's fully covered by your gold's value.",
      imageSrc: "/goldexchangepage/image4.png",
    },
  ];
  return (
    <div className=" relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden">
      <div className="flex flex-col justify-center items-center">
        <h1 className=" text-xl md:text-2xl lg:text-[28px] font-lora font-semibold z-10">
          Exchange Old Gold, Shine with Something New
        </h1>
        <p className="text-sm lg:text-base text-custom-textgray max-w-6xl z-10">
          Give your old gold a fresh story. At Ridhi Sidhi, we make it easy to
          exchange your existing gold jewellery for brand new designs. With
          transparent evaluations and live gold rates, you'll always get the
          best value for your gold - no hidden fees, no complicated processes,
          just honest sparkle.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-14 pb-8 z-10">
          {stepCardData.map((item, index) => (
            <StepCard
              key={index}
              stepText={item.stepText}
              description={item.description}
              imageSrc={item.imageSrc}
            />
          ))}
        </div>
      </div>
      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default HeroSection;

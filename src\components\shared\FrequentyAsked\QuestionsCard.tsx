import React from "react";

const QuestionsCard = () => {
  const Question = () => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="61"
        height="61"
        viewBox="0 0 61 61"
        fill="none"
      >
        <g clipPath="url(#clip0_202_2747)">
          <path
            d="M51.7473 10.5209H47.8402V8.80755C47.8402 4.22674 44.1134 0.5 39.5326 0.5H15.0377C14.5523 0.5 14.1588 0.893522 14.1588 1.37892C14.1588 1.86432 14.5523 2.25784 15.0377 2.25784H39.5326C43.1442 2.25784 46.0823 5.19601 46.0823 8.80755V34.4134C46.0823 38.025 43.1442 40.9631 39.5326 40.9631H25.7805C25.6114 40.9631 25.4459 41.0119 25.3039 41.1035L13.9414 48.4371C13.3606 48.8119 12.663 48.8181 12.0752 48.454C11.4879 48.0901 11.1831 47.4629 11.26 46.776L11.8002 41.9396C11.828 41.691 11.7486 41.4424 11.5818 41.256C11.415 41.0695 11.1768 40.963 10.9267 40.963H9.25091C5.63937 40.963 2.7012 38.0248 2.7012 34.4133V8.80755C2.7012 5.19601 5.63937 2.25784 9.25091 2.25784H10.9361C11.4215 2.25784 11.815 1.86432 11.815 1.37892C11.815 0.893522 11.4215 0.5 10.9361 0.5H9.25091C4.6701 0.5 0.943359 4.22674 0.943359 8.80755V34.4134C0.943359 38.9942 4.6701 42.721 9.25091 42.721H9.9442L9.51306 46.5807C9.36119 47.9385 9.98815 49.2288 11.1494 49.9483C11.7208 50.3023 12.3584 50.479 12.9956 50.479C13.6535 50.479 14.3109 50.2905 14.8944 49.9142L15.1069 49.7771C16.677 51.6469 18.9964 52.7417 21.4667 52.7417H34.9599L39.1908 55.4725C39.3381 55.5676 39.5033 55.613 39.6666 55.613C39.9551 55.613 40.2378 55.4711 40.4058 55.2105C40.6691 54.8027 40.5518 54.2587 40.144 53.9954L35.6955 51.1243C35.5535 51.0326 35.388 50.9839 35.2189 50.9839H21.4667C19.5946 50.9839 17.8333 50.185 16.5997 48.8134L26.0395 42.7209H39.5326C44.1134 42.7209 47.8402 38.9941 47.8402 34.4133V12.2787H51.7473C55.3594 12.2787 58.2982 15.2175 58.2982 18.8296V44.4343C58.2982 48.0458 55.3594 50.984 51.7473 50.984H50.0726C49.8226 50.984 49.5843 51.0905 49.4176 51.277C49.2508 51.4633 49.1713 51.712 49.1991 51.9605L49.7394 56.7983C49.8162 57.4844 49.5115 58.1111 48.9245 58.4749C48.3367 58.8392 47.639 58.8327 47.0582 58.4582L43.5812 56.214C43.1735 55.9508 42.6294 56.068 42.366 56.4758C42.1027 56.8837 42.22 57.4276 42.6278 57.691L46.1051 59.9353C46.6886 60.3116 47.3458 60.5001 48.0038 60.5C48.6409 60.5 49.2788 60.3232 49.8503 59.969C51.0113 59.2497 51.6382 57.9597 51.4863 56.6029L51.0552 52.7418H51.7473C56.3287 52.7418 60.056 49.0151 60.056 44.4343V18.8296C60.056 14.2482 56.3287 10.5209 51.7473 10.5209Z"
            fill="#C51D24"
          />
          <path
            d="M16.881 18.9302C18.9332 18.9302 20.6029 17.2605 20.6029 15.2083C20.6029 15.0295 20.6154 14.8504 20.6402 14.6746C20.9122 12.7238 22.6754 11.2973 24.6539 11.4282C26.5217 11.5508 28.0327 13.0507 28.1686 14.9173C28.2932 16.625 27.2587 18.2144 25.6526 18.7825C22.6721 19.8358 20.6696 22.6789 20.6696 25.8573V27.0632C20.6696 29.1154 22.3393 30.7851 24.3915 30.7851C26.4439 30.7851 28.1135 29.1154 28.1135 27.0632V25.8573C28.1135 25.8216 28.1308 25.8033 28.1348 25.8012C28.8896 25.534 29.6129 25.1871 30.2845 24.77C30.6969 24.514 30.8236 23.9721 30.5675 23.5597C30.3114 23.1473 29.7696 23.0205 29.3572 23.2767C28.7928 23.6272 28.1841 23.919 27.55 24.1434C26.8356 24.3947 26.3556 25.0834 26.3556 25.8573V27.0632C26.3556 28.1461 25.4745 29.0273 24.3915 29.0273C23.3086 29.0273 22.4275 28.1461 22.4275 27.0632V25.8573C22.4275 23.4225 23.9589 21.2453 26.2386 20.4398C28.5895 19.6083 30.1041 17.2847 29.9217 14.7895C29.7225 12.0531 27.5074 9.85404 24.7692 9.67427C21.8763 9.48407 19.2977 11.5734 18.8993 14.4304C18.8633 14.6859 18.845 14.9476 18.845 15.2083C18.845 16.2913 17.9638 17.1724 16.8809 17.1724C15.7978 17.1724 14.9168 16.2913 14.9168 15.2083C14.9168 14.7708 14.9475 14.3267 15.008 13.8896C15.6901 9.00372 20.0906 5.43026 25.0255 5.75394C29.7099 6.06062 33.4996 9.82205 33.8406 14.5035C34.0032 16.729 33.368 18.9544 32.052 20.7699C31.7671 21.1629 31.8547 21.7124 32.2478 21.9973C32.641 22.2822 33.1904 22.1945 33.4754 21.8015C35.0342 19.6508 35.7866 17.0136 35.5939 14.3756C35.1896 8.82406 30.6956 4.36349 25.1407 3.99973C19.2922 3.61582 14.076 7.85315 13.2671 13.6475C13.1955 14.1652 13.1592 14.6903 13.1592 15.2081C13.1591 17.2605 14.8287 18.9302 16.881 18.9302Z"
            fill="#C51D24"
          />
          <path
            d="M24.3919 31.7773C22.3396 31.7773 20.6699 33.447 20.6699 35.4993V35.5229C20.6699 37.5752 22.3396 39.2449 24.3919 39.2449C26.4442 39.2449 28.1139 37.5752 28.1139 35.5229V35.4993C28.1139 33.447 26.4442 31.7773 24.3919 31.7773ZM26.3561 35.5229C26.3561 36.6059 25.475 37.4871 24.3919 37.4871C23.3089 37.4871 22.4277 36.6059 22.4277 35.5229V35.4993C22.4277 34.4163 23.3089 33.5352 24.3919 33.5352C25.475 33.5352 26.3561 34.4163 26.3561 35.4993V35.5229Z"
            fill="#C51D24"
          />
        </g>
        <defs>
          <clipPath id="clip0_202_2747">
            <rect
              width="60"
              height="60"
              fill="white"
              transform="translate(0.5 0.5)"
            />
          </clipPath>
        </defs>
      </svg>
    );
  };
  return (
    <div className="p-4 border border-gray-200 shadow-md rounded-xl flex flex-col gap-4 justify-center items-center h-full w-full">
      <Question />
      <h2 className="text-custom-red-500 font-lora font-semibold text-lg md:text-xl lg:text-[22px]">
        Still have questions?
      </h2>
      <p className="font-lora text-sm lg:text-base max-w-sm">
        Our team is here to assist you with quick and accurate responses.
      </p>
      <div className="bg-custom-red-500 rounded-xl px-8 py-3">
        <p className="text-white font-lora text-xs lg:text-sm ">
          +977-9876543210
        </p>
      </div>
    </div>
  );
};

export default QuestionsCard;

"use client";
import Image from "next/image";
import React, { useRef } from "react";
import Slider from "react-slick";
import { secondarySectionSlidersettings } from "./home.obj";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import get from "lodash/get";
const slickContent = [
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
];
const SecondarySection = () => {
  const sliderRef = useRef<Slider>(null);

  const goToPrev = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };
  return (
    <section className="container mx-auto md:p-4 p-2 grid grid-cols-1 md:grid-cols-2 gap-y-4">
      <Image
        src="/images/secondary.png"
        alt="secondary.png"
        width={600}
        height={400}
      />
      <div className="flex flex-col w-full py-6 justify-between">
        <Slider ref={sliderRef} {...secondarySectionSlidersettings}>
          {slickContent.map((item, index) => (
            <div key={index}>
              <Image
                src={item.img}
                alt="jhumka.png"
                className="rounded-sm border-1 border-custom-red-500"
                width={130}
                height={130}
              />
              <p className="text-custom-red-500 font-semibold text-lg">
                Price: {item.price}
              </p>
              <p className="text-xs line-clamp-1">
                {get(item, "description", "N/A")}
              </p>
            </div>
          ))}
        </Slider>
        <div className="flex items-center justify-between py-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className=" bg-custom-red-500 border border-custom-red-500 backdrop-blur-sm hover:bg-custom-red-600 border-none rounded-full shadow-md"
              onClick={goToPrev}
            >
              <ChevronLeft className="size-4 text-white" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="bg-custom-red-500 border border-custom-red-500 backdrop-blur-sm hover:bg-custom-red-600 border-none rounded-full shadow-md"
              onClick={goToNext}
            >
              <ChevronRight className="size-4 text-white" />
            </Button>
          </div>
          <Button className="bg-custom-red-500 hover:bg-custom-red-600 text-white">
            Explore All Collections
          </Button>
        </div>
      </div>
    </section>
  );
};

export default SecondarySection;

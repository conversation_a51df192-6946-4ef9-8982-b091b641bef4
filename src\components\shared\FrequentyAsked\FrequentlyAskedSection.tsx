// import React from "react";
// import QuestionsCard from "./QuestionsCard";
// import { frequentlyAskedData } from "./FrequentAskedObj";
// import DropText from "./DropText";

// const FrequentlyAskedSection = () => {
//   return (
//     <div className="py-4 px-4 container mx-auto md:px-14 lg:px-16 flex items-center gap-8 ">
//       <div className="flex flex-col gap-3 w-full md:w-1/2">
//         {frequentlyAskedData.map((item, index) => (
//           <DropText
//             key={index}
//             title={item.title}
//             description={item.description}
//           />
//         ))}
//       </div>
//       <div className="w-full md:w-1/2">
//         <QuestionsCard />
//       </div>
//     </div>
//   );
// };

// export default FrequentlyAskedSection;

import React from "react";
import QuestionsCard from "./QuestionsCard";
import { frequentlyAskedData } from "./FrequentAskedObj";
import DropText from "./DropText";

const FrequentlyAskedSection = () => {
  return (
    <div className="py-4 px-4 container mx-auto md:px-14 lg:px-16 grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className=" flex flex-col gap-3">
        {frequentlyAskedData.map((item, index) => (
          <DropText
            key={index}
            title={item.title}
            description={item.description}
          />
        ))}
      </div>
      <div>
        <QuestionsCard />
      </div>
    </div>
  );
};

export default FrequentlyAskedSection;

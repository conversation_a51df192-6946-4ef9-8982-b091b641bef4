import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function Marketing() {
  return (
    <div className="container mx-auto p-4 grid grid-cols-1 lg:grid-cols-2 gap-2">
      {/* Main Hero Section */}
      <Card className="relative overflow-hidden bg-gradient-to-br from-orange-200 to-orange-300 min-h-[400px] lg:min-h-[500px]">
        <div className="absolute inset-0">
          <Image
            src="/images/girly.png"
            alt="Woman wearing elegant jewelry"
            fill
            className="object-cover"
          />
        </div>
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 p-8 h-full flex flex-col justify-end">
          <div className="text-white">
            <h1 className="text-3xl lg:text-4xl font-bold mb-2 tracking-wide">
              BRILLIANCE
            </h1>
            <h2 className="text-2xl lg:text-3xl font-light tracking-wider">
              WITH A CONSCIENCE
            </h2>
          </div>
        </div>
      </Card>

      {/* Right Side Sections */}
      <div className="grid grid-rows-2 gap-2">
        <Card className="relative overflow-hidden bg-gradient-to-r from-gray-100 to-gray-200 p-6">
          <Image
            src="/images/mk1.png"
            alt="Woman wearing elegant jewelry"
            fill
            className=" absolute inset-0"
          />
        </Card>

        <Card className="relative overflow-hidden bg-gradient-to-br from-amber-50 to-amber-100 p-6 min-h-[200px]">
          <Image
            src="/images/mk2.png"
            alt="Woman wearing elegant jewelry"
            fill
            className=" absolute inset-0"
          />
        </Card>
      </div>
    </div>
  );
}

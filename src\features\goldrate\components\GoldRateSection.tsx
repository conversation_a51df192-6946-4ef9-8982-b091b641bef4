// import React from "react";
// import GoldRateCard from "./GoldRateCard";
// import CollectionSlider from "@/components/shared/CollectionSlider";
// import GoldPriceTable from "./GoldPriceTableCard";
// import { bannerDetails, goldPrices } from "./goldRateObj";
// import GoldRateBanner from "./GoldRateBanner";

// const GoldRateSection = () => {
//   return (
//     <div className="p-4 container mx-auto  md:p-14 lg:p-16 flex flex-col md:flex-row items-center gap-4">
//       <div className="md:w-1/2">
//         <GoldRateCard />
//         <CollectionSlider />
//       </div>
//       <div className="md:w-1/2 space-y-2">
//         <GoldPriceTable
//           title={goldPrices.title}
//           lastUpdated={goldPrices.lastUpdated}
//           headers={goldPrices.headers}
//           items={goldPrices.items}
//         />
//         <GoldRateBanner {...bannerDetails} />
//       </div>
//     </div>
//   );
// };

// export default GoldRateSection;

import React from "react";
import GoldRateCard from "./GoldRateCard";
import CollectionSlider from "@/components/shared/CollectionSlider";
import GoldPriceTable from "./GoldPriceTableCard";
import { bannerDetails, goldPrices } from "./goldRateObj";
import GoldRateBanner from "./GoldRateBanner";

const GoldRateSection = () => {
  return (
    <div className="py-4 px-4 container mx-auto md:px-14 lg:px-16 grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-4">
        <GoldRateCard />
        <CollectionSlider />
      </div>
      <div className="space-y-2">
        <GoldPriceTable
          title={goldPrices.title}
          lastUpdated={goldPrices.lastUpdated}
          headers={goldPrices.headers}
          items={goldPrices.items}
        />
        <GoldRateBanner {...bannerDetails} />
      </div>
    </div>
  );
};

export default GoldRateSection;

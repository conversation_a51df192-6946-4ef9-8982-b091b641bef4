import Link from "next/link";
import React from "react";

const routes = [
  { label: "Profile", url: "/dashboard/profile" },
  { label: "Orders", url: "/dashboard/orders" },
  { label: "Cart", url: "/dashboard/cart" },
  { label: "Whishlist", url: "/dashboard/wishlist" },
];
const UserProfile = () => {
  return (
    <aside className="bg-violet-50 h-screen max-w-62 w-full px-4 py-6">
      <h3 className="font-lg text-custom-red-500">Admin</h3>
      <p className="text-sm"><EMAIL></p>
      <div className="flex flex-col mt-4 space-y-1">
        {routes.map((item, index) => (
          <Link
            href={item.url}
            key={index.toString()}
            className="text-base text-custom-red-500"
          >
            {item.label}
          </Link>
        ))}
      </div>
    </aside>
  );
};

export default UserProfile;

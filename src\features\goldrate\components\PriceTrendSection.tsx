import React from "react";
import PriceTrendChart from "./PriceTrends";
import { chartData } from "./goldRateObj";
import Image from "next/image";

const PriceTrendSection = () => {
  const images = ["/goldratepage/gold1.png", "/goldratepage/gold2.png"];
  return (
    <div className=" py-4 px-4 container mx-auto md:px-14 lg:px-16  grid grid-cols-1 md:grid-cols-12 gap-4">
      <div className="col-span-12 md:col-span-8 h-full w-full">
        <PriceTrendChart {...chartData} />
      </div>
      <div className="col-span-12 md:col-span-4 flex flex-col space-y-1">
        {images.map((item, index) => (
          <Image
            key={index}
            src={item}
            alt={`Gold Image ${index + 1}`}
            height={900}
            width={900}
            className="rounded-xl"
          />
        ))}
      </div>
    </div>
  );
};

export default PriceTrendSection;

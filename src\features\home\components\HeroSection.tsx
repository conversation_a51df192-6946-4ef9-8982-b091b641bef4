"use client";
import React, { useRef } from "react";
import { heroSectionSlidersettings } from "./home.obj";
import Slider from "react-slick";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

const img = [
  "/images/banner.png",
  "/images/banner.png",
  "/images/banner.png",
  "/images/banner.png",
  "/images/banner.png",
  "/images/banner.png",
  "/images/banner.png",
  "/images/banner.png",
];

const HeroSection = () => {
  const sliderRef = useRef<Slider>(null);

  const goToPrev = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  return (
    <section className="w-full p-2 md:p-3 relative">
      <Slider ref={sliderRef} className="h-auto" {...heroSectionSlidersettings}>
        {img.map((item, index) => (
          <Image
            key={index}
            src={item}
            alt="banner"
            className="rounded-md"
            width={1000}
            height={200}
          />
        ))}
      </Slider>

      {/* Custom navigation buttons */}
      <div className="absolute top-1/2 left-0 right-0 flex justify-between transform -translate-y-1/2 px-4 md:px-8 z-10">
        <Button
          variant="outline"
          size="icon"
          className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md"
          onClick={goToPrev}
        >
          <ChevronLeft className="h-5 w-5 text-custom-red-500" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md"
          onClick={goToNext}
        >
          <ChevronRight className="h-5 w-5 text-custom-red-500" />
        </Button>
      </div>
    </section>
  );
};

export default HeroSection;

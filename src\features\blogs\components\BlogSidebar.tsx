"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Facebook, Instagram, Mail, MessageCircle } from "lucide-react";
import { InputField } from "@/components/ui/form-field";
import { RecentPost, SocialLink } from "../types";

// Sample recent posts data
const recentPosts: RecentPost[] = [
  {
    id: 1,
    title:
      "The Ultimate Engagement Ring Guide: Choosing the Perfect Symbol of Love",
    date: "September 27, 2023",
  },
  {
    id: 2,
    title:
      "The Ultimate Engagement Ring Guide: Choosing the Perfect Symbol of Love",
    date: "September 27, 2023",
  },
  {
    id: 3,
    title:
      "The Ultimate Engagement Ring Guide: Choosing the Perfect Symbol of Love",
    date: "September 27, 2023",
  },
];

// Social media links
const socialLinks: SocialLink[] = [
  {
    name: "WhatsApp",
    icon: MessageCircle,
    href: "#",
    color: "text-green-500 hover:text-green-600",
  },
  {
    name: "<PERSON><PERSON>",
    icon: Mail,
    href: "#",
    color: "text-blue-500 hover:text-blue-600",
  },
  {
    name: "Facebook",
    icon: Facebook,
    href: "#",
    color: "text-blue-600 hover:text-blue-700",
  },
  {
    name: "Instagram",
    icon: Instagram,
    href: "#",
    color: "text-pink-500 hover:text-pink-600",
  },
];

const BlogSidebar = () => {
  return (
    <div className="space-y-4">
      {/* Search Section */}
      <div className="relative">
        <InputField
          placeholder="Search here..."
          className="pr-10"
          leftSection={<Search className="h-4 w-4" />}
        />
      </div>

      {/* Recent Posts Section */}
      <div>
        <h5 className="text-lg font-semibold text-center text-gray-600 pb-3 border-b">
          Recent Posts
        </h5>
        <div className="py-2">
          <div className="space-y-2">
            {recentPosts.map((post) => (
              <div key={post.id} className=" bg-gray-100 rounded-sm p-2 ">
                <h4 className="text-sm font-medium mb-2 line-clamp-3 hover:text-custom-blue-500 cursor-pointer transition-colors">
                  {post.title}
                </h4>
                <p className="text-xs text-muted-foreground">{post.date}</p>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <Button
              variant="link"
              className="p-0 h-auto text-custom-blue-500 hover:text-custom-blue-600 font-medium"
            >
              View More →
            </Button>
          </div>
        </div>
      </div>

      {/* Stay Connected Section */}
      <div>
        <h5 className="text-lg font-semibold text-gray-600 text-center pb-3 border-b">
          Stay Connected
        </h5>
        <div className="grid grid-cols-4 py-2 gap-4">
          {socialLinks.map((social) => {
            const IconComponent = social.icon;
            return (
              <a
                key={social.name}
                href={social.href}
                className={`flex flex-col items-center  text-custom-red-500 hover:text-custom-red-600`}
              >
                <IconComponent className="w-6 h-6 mb-2" />
                <span className="text-xs font-medium ">{social.name}</span>
              </a>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default BlogSidebar;

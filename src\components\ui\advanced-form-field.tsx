"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, CheckCircle, Eye, EyeOff, X } from "lucide-react";

// Validation states
type ValidationState = "default" | "valid" | "invalid" | "warning";

// Base advanced form field props
interface BaseAdvancedFormFieldProps {
  label?: string;
  description?: string;
  error?: string;
  warning?: string;
  success?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  labelClassName?: string;
  descriptionClassName?: string;
  errorClassName?: string;
  warningClassName?: string;
  successClassName?: string;
  withAsterisk?: boolean;
  size?: "sm" | "md" | "lg";
  validationState?: ValidationState;
  withCharacterCount?: boolean;
  maxLength?: number;
  clearable?: boolean;
  onClear?: () => void;
}

// Advanced Input field props
interface AdvancedInputFieldProps extends BaseAdvancedFormFieldProps {
  type?: React.HTMLInputTypeAttribute;
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  leftSection?: React.ReactNode;
  rightSection?: React.ReactNode;
  withPasswordToggle?: boolean;
  inputClassName?: string;
  id?: string;
  name?: string;
  autoComplete?: string;
  autoFocus?: boolean;
  readOnly?: boolean;
  minLength?: number;
  pattern?: string;
}

// Advanced Textarea field props
interface AdvancedTextareaFieldProps extends BaseAdvancedFormFieldProps {
  placeholder?: string;
  value?: string;
  defaultValue?: string;
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  rows?: number;
  cols?: number;
  resize?: "none" | "both" | "horizontal" | "vertical";
  textareaClassName?: string;
  id?: string;
  name?: string;
  autoFocus?: boolean;
  readOnly?: boolean;
  minLength?: number;
  autoResize?: boolean;
}

// Size variants
const sizeVariants = {
  sm: {
    input: "h-8 px-2.5 py-1.5 text-sm",
    textarea: "px-2.5 py-1.5 text-sm min-h-12",
    label: "text-sm",
    description: "text-xs",
    message: "text-xs",
  },
  md: {
    input: "h-9 px-3 py-2 text-sm",
    textarea: "px-3 py-2 text-sm min-h-16",
    label: "text-sm",
    description: "text-xs",
    message: "text-xs",
  },
  lg: {
    input: "h-10 px-4 py-2.5 text-base",
    textarea: "px-4 py-2.5 text-base min-h-20",
    label: "text-base",
    description: "text-sm",
    message: "text-sm",
  },
};

// Validation state styles
const validationStyles = {
  default: {
    border: "",
    ring: "",
    icon: null,
    iconColor: "",
  },
  valid: {
    border: "border-green-500",
    ring: "focus-visible:border-green-500 focus-visible:ring-green-500/20",
    icon: CheckCircle,
    iconColor: "text-green-500",
  },
  invalid: {
    border: "border-custom-red-500",
    ring: "focus-visible:border-custom-red-500 focus-visible:ring-custom-red-500/20",
    icon: AlertCircle,
    iconColor: "text-custom-red-500",
  },
  warning: {
    border: "border-yellow-500",
    ring: "focus-visible:border-yellow-500 focus-visible:ring-yellow-500/20",
    icon: AlertCircle,
    iconColor: "text-yellow-500",
  },
};

// Get validation state based on props
const getValidationState = (
  validationState?: ValidationState,
  error?: string,
  warning?: string,
  success?: string
): ValidationState => {
  if (validationState) return validationState;
  if (error) return "invalid";
  if (warning) return "warning";
  if (success) return "valid";
  return "default";
};

// Advanced Input Field Component
const AdvancedInputField = React.forwardRef<
  HTMLInputElement,
  AdvancedInputFieldProps
>(
  (
    {
      label,
      description,
      error,
      warning,
      success,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      warningClassName,
      successClassName,
      inputClassName,
      withAsterisk,
      size = "md",
      validationState,
      withCharacterCount = false,
      maxLength,
      clearable = false,
      onClear,
      type = "text",
      leftSection,
      rightSection,
      withPasswordToggle = false,
      value = "",
      id,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [internalType, setInternalType] = React.useState(type);
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const messageId =
      error || warning || success ? `${fieldId}-message` : undefined;

    // Handle password toggle
    React.useEffect(() => {
      if (withPasswordToggle && type === "password") {
        setInternalType(showPassword ? "text" : "password");
      } else {
        setInternalType(type);
      }
    }, [showPassword, type, withPasswordToggle]);

    const togglePassword = () => {
      setShowPassword(!showPassword);
    };

    const handleClear = () => {
      onClear?.();
    };

    const currentValidationState = getValidationState(
      validationState,
      error,
      warning,
      success
    );
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];
    const validation = validationStyles[currentValidationState];
    const ValidationIcon = validation.icon;

    // Character count
    const characterCount = typeof value === "string" ? value.length : 0;
    const showCharacterCount = withCharacterCount && maxLength;

    // Determine right section content
    const getRightSection = () => {
      const sections = [];

      // Clear button
      if (clearable && value && !disabled) {
        sections.push(
          <button
            key="clear"
            type="button"
            onClick={handleClear}
            className="text-muted-foreground hover:text-foreground transition-colors p-1"
            tabIndex={-1}
          >
            <X className="h-3 w-3" />
          </button>
        );
      }

      // Password toggle
      if (withPasswordToggle && type === "password") {
        sections.push(
          <button
            key="password-toggle"
            type="button"
            onClick={togglePassword}
            className="text-muted-foreground hover:text-foreground transition-colors p-1"
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        );
      }

      // Validation icon
      if (ValidationIcon && currentValidationState !== "default") {
        sections.push(
          <ValidationIcon
            key="validation"
            className={cn("h-4 w-4", validation.iconColor)}
          />
        );
      }

      // Custom right section
      if (rightSection) {
        sections.push(
          <div key="custom" className="flex items-center">
            {rightSection}
          </div>
        );
      }

      return sections.length > 0 ? (
        <div className="flex items-center gap-1">{sections}</div>
      ) : null;
    };

    const finalRightSection = getRightSection();

    return (
      <div className={cn("space-y-2", className)}>
        {/* Label */}
        {label && (
          <div className="flex items-center justify-between">
            <Label
              htmlFor={fieldId}
              className={cn(
                sizes.label,
                "font-medium",
                currentValidationState === "invalid" && "text-custom-red-500",
                disabled && "opacity-50",
                labelClassName
              )}
            >
              {label}
              {isRequired && (
                <span
                  className="text-custom-red-500 ml-1"
                  aria-label="required"
                >
                  *
                </span>
              )}
            </Label>

            {/* Character count */}
            {showCharacterCount && (
              <span
                className={cn(
                  sizes.message,
                  "text-muted-foreground",
                  characterCount > maxLength! && "text-custom-red-500"
                )}
              >
                {characterCount}/{maxLength}
              </span>
            )}
          </div>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Section */}
          {leftSection && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none">
              {leftSection}
            </div>
          )}

          {/* Input */}
          <Input
            ref={ref}
            id={fieldId}
            type={internalType}
            value={value}
            disabled={disabled}
            required={required}
            maxLength={maxLength}
            aria-invalid={currentValidationState === "invalid"}
            aria-describedby={cn(
              descriptionId && descriptionId,
              messageId && messageId
            )}
            className={cn(
              sizes.input,
              leftSection && "pl-10",
              finalRightSection && "pr-10",
              validation.border,
              validation.ring,
              inputClassName
            )}
            {...props}
          />

          {/* Right Section */}
          {finalRightSection && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {finalRightSection}
            </div>
          )}
        </div>

        {/* Messages */}
        {(error || warning || success) && (
          <div
            id={messageId}
            className={cn(
              sizes.message,
              "flex items-center gap-1",
              error && cn("text-custom-red-500", errorClassName),
              warning && cn("text-yellow-600", warningClassName),
              success && cn("text-green-600", successClassName)
            )}
            role="alert"
            aria-live="polite"
          >
            {error && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {warning && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {success && <CheckCircle className="h-3 w-3 flex-shrink-0" />}
            {error || warning || success}
          </div>
        )}
      </div>
    );
  }
);

AdvancedInputField.displayName = "AdvancedInputField";

// Advanced Textarea Field Component
const AdvancedTextareaField = React.forwardRef<
  HTMLTextAreaElement,
  AdvancedTextareaFieldProps
>(
  (
    {
      label,
      description,
      error,
      warning,
      success,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      warningClassName,
      successClassName,
      textareaClassName,
      withAsterisk,
      size = "md",
      validationState,
      withCharacterCount = false,
      maxLength,
      clearable = false,
      onClear,
      resize = "vertical",
      autoResize = false,
      value = "",
      id,
      ...props
    },
    ref
  ) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const messageId =
      error || warning || success ? `${fieldId}-message` : undefined;

    // Auto-resize functionality
    React.useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current;
        textarea.style.height = "auto";
        textarea.style.height = `${textarea.scrollHeight}px`;
      }
    }, [value, autoResize]);

    // Combine refs
    React.useImperativeHandle(ref, () => textareaRef.current!);

    const handleClear = () => {
      onClear?.();
      if (autoResize && textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    };

    const currentValidationState = getValidationState(
      validationState,
      error,
      warning,
      success
    );
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];
    const validation = validationStyles[currentValidationState];
    const ValidationIcon = validation.icon;

    // Character count
    const characterCount = typeof value === "string" ? value.length : 0;
    const showCharacterCount = withCharacterCount && maxLength;

    // Resize class mapping
    const resizeClasses = {
      none: "resize-none",
      both: "resize",
      horizontal: "resize-x",
      vertical: "resize-y",
    };

    return (
      <div className={cn("space-y-2", className)}>
        {/* Label */}
        {label && (
          <div className="flex items-center justify-between">
            <Label
              htmlFor={fieldId}
              className={cn(
                sizes.label,
                "font-medium",
                currentValidationState === "invalid" && "text-custom-red-500",
                disabled && "opacity-50",
                labelClassName
              )}
            >
              {label}
              {isRequired && (
                <span
                  className="text-custom-red-500 ml-1"
                  aria-label="required"
                >
                  *
                </span>
              )}
            </Label>

            {/* Character count */}
            {showCharacterCount && (
              <span
                className={cn(
                  sizes.message,
                  "text-muted-foreground",
                  characterCount > maxLength! && "text-custom-red-500"
                )}
              >
                {characterCount}/{maxLength}
              </span>
            )}
          </div>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Textarea Container */}
        <div className="relative">
          {/* Textarea */}
          <Textarea
            ref={textareaRef}
            id={fieldId}
            value={value}
            disabled={disabled}
            required={required}
            maxLength={maxLength}
            aria-invalid={currentValidationState === "invalid"}
            aria-describedby={cn(
              descriptionId && descriptionId,
              messageId && messageId
            )}
            className={cn(
              sizes.textarea,
              autoResize
                ? "resize-none overflow-hidden"
                : resizeClasses[resize],
              validation.border,
              validation.ring,
              clearable && value && !disabled && "pr-10",
              textareaClassName
            )}
            {...props}
          />

          {/* Clear button */}
          {clearable && value && !disabled && (
            <button
              type="button"
              onClick={handleClear}
              className="absolute top-3 right-3 text-muted-foreground hover:text-foreground transition-colors p-1"
              tabIndex={-1}
            >
              <X className="h-3 w-3" />
            </button>
          )}

          {/* Validation icon */}
          {ValidationIcon &&
            currentValidationState !== "default" &&
            !clearable && (
              <div className="absolute top-3 right-3">
                <ValidationIcon
                  className={cn("h-4 w-4", validation.iconColor)}
                />
              </div>
            )}
        </div>

        {/* Messages */}
        {(error || warning || success) && (
          <div
            id={messageId}
            className={cn(
              sizes.message,
              "flex items-center gap-1",
              error && cn("text-custom-red-500", errorClassName),
              warning && cn("text-yellow-600", warningClassName),
              success && cn("text-green-600", successClassName)
            )}
            role="alert"
            aria-live="polite"
          >
            {error && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {warning && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {success && <CheckCircle className="h-3 w-3 flex-shrink-0" />}
            {error || warning || success}
          </div>
        )}
      </div>
    );
  }
);

AdvancedTextareaField.displayName = "AdvancedTextareaField";

export {
  AdvancedInputField,
  AdvancedTextareaField,
  type AdvancedInputFieldProps,
  type AdvancedTextareaFieldProps,
};

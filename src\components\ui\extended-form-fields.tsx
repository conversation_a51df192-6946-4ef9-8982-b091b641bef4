"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, CheckCircle } from "lucide-react";
import Select from "@/components/Select";

// Base form field props
interface BaseFormFieldProps {
  label?: string;
  description?: string;
  error?: string;
  warning?: string;
  success?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  labelClassName?: string;
  descriptionClassName?: string;
  errorClassName?: string;
  warningClassName?: string;
  successClassName?: string;
  withAsterisk?: boolean;
  size?: "sm" | "md" | "lg";
}

// Radio button option
interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

// Checkbox option
interface CheckboxOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

// Select option
interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// Radio Group Props
interface RadioGroupFieldProps extends BaseFormFieldProps {
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  orientation?: "horizontal" | "vertical";
  name?: string;
  id?: string;
}

// Checkbox Group Props
interface CheckboxGroupFieldProps extends BaseFormFieldProps {
  options: CheckboxOption[];
  value?: string[];
  onChange?: (value: string[]) => void;
  orientation?: "horizontal" | "vertical";
  name?: string;
  id?: string;
}

// Single Checkbox Props
interface CheckboxFieldProps extends BaseFormFieldProps {
  value?: boolean;
  onChange?: (checked: boolean) => void;
  name?: string;
  id?: string;
  checkboxLabel?: string;
}

// Select Props
interface SelectFieldProps extends BaseFormFieldProps {
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  name?: string;
  id?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
}

// Size variants
const sizeVariants = {
  sm: {
    label: "text-sm",
    description: "text-xs",
    message: "text-xs",
    checkbox: "h-3 w-3",
    radio: "h-3 w-3",
  },
  md: {
    label: "text-sm",
    description: "text-xs",
    message: "text-xs",
    checkbox: "h-4 w-4",
    radio: "h-4 w-4",
  },
  lg: {
    label: "text-base",
    description: "text-sm",
    message: "text-sm",
    checkbox: "h-5 w-5",
    radio: "h-5 w-5",
  },
};

// Get validation state
const getValidationState = (
  error?: string,
  warning?: string,
  success?: string
) => {
  if (error) return "invalid";
  if (warning) return "warning";
  if (success) return "valid";
  return "default";
};

// Radio Group Component
const RadioGroupField = React.forwardRef<HTMLDivElement, RadioGroupFieldProps>(
  (
    {
      label,
      description,
      error,
      warning,
      success,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      warningClassName,
      successClassName,
      withAsterisk,
      size = "md",
      options,
      value,
      onChange,
      orientation = "vertical",
      name,
      id,
    },
    ref
  ) => {
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const messageId =
      error || warning || success ? `${fieldId}-message` : undefined;

    const validationState = getValidationState(error, warning, success);
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];

    const handleChange = (optionValue: string) => {
      if (!disabled && onChange) {
        onChange(optionValue);
      }
    };

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {/* Label */}
        {label && (
          <Label
            className={cn(
              sizes.label,
              "font-medium",
              validationState === "invalid" && "text-custom-red-500",
              disabled && "opacity-50",
              labelClassName
            )}
          >
            {label}
            {isRequired && (
              <span className="text-custom-red-500 ml-1" aria-label="required">
                *
              </span>
            )}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Radio Options */}
        <div
          className={cn(
            "space-y-3",
            orientation === "horizontal" && "flex flex-wrap gap-6 space-y-0"
          )}
          role="radiogroup"
          aria-describedby={cn(
            descriptionId && descriptionId,
            messageId && messageId
          )}
        >
          {options.map((option, index) => (
            <div key={option.value} className="flex items-start space-x-2">
              <div className="flex items-center h-5">
                <input
                  type="radio"
                  id={`${fieldId}-${index}`}
                  name={name || fieldId}
                  value={option.value}
                  checked={value === option.value}
                  onChange={() => handleChange(option.value)}
                  disabled={disabled || option.disabled}
                  className={cn(
                    sizes.radio,
                    "text-custom-blue-500 border-gray-300 focus:ring-custom-blue-500 focus:ring-2",
                    validationState === "invalid" && "border-custom-red-500",
                    (disabled || option.disabled) &&
                      "opacity-50 cursor-not-allowed"
                  )}
                />
              </div>
              <div className="flex-1">
                <Label
                  htmlFor={`${fieldId}-${index}`}
                  className={cn(
                    sizes.label,
                    "font-normal cursor-pointer",
                    (disabled || option.disabled) &&
                      "opacity-50 cursor-not-allowed"
                  )}
                >
                  {option.label}
                </Label>
                {option.description && (
                  <p
                    className={cn(
                      sizes.description,
                      "text-muted-foreground mt-1"
                    )}
                  >
                    {option.description}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Messages */}
        {(error || warning || success) && (
          <div
            id={messageId}
            className={cn(
              sizes.message,
              "flex items-center gap-1",
              error && cn("text-custom-red-500", errorClassName),
              warning && cn("text-yellow-600", warningClassName),
              success && cn("text-green-600", successClassName)
            )}
            role="alert"
            aria-live="polite"
          >
            {error && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {warning && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {success && <CheckCircle className="h-3 w-3 flex-shrink-0" />}
            {error || warning || success}
          </div>
        )}
      </div>
    );
  }
);

RadioGroupField.displayName = "RadioGroupField";

// Checkbox Group Component
const CheckboxGroupField = React.forwardRef<
  HTMLDivElement,
  CheckboxGroupFieldProps
>(
  (
    {
      label,
      description,
      error,
      warning,
      success,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      warningClassName,
      successClassName,
      withAsterisk,
      size = "md",
      options,
      value = [],
      onChange,
      orientation = "vertical",
      name,
      id,
    },
    ref
  ) => {
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const messageId =
      error || warning || success ? `${fieldId}-message` : undefined;

    const validationState = getValidationState(error, warning, success);
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];

    const handleChange = (optionValue: string, checked: boolean) => {
      if (!disabled && onChange) {
        if (checked) {
          onChange([...value, optionValue]);
        } else {
          onChange(value.filter((v) => v !== optionValue));
        }
      }
    };

    return (
      <div ref={ref} className={cn("space-y-2", className)}>
        {/* Label */}
        {label && (
          <Label
            className={cn(
              sizes.label,
              "font-medium",
              validationState === "invalid" && "text-custom-red-500",
              disabled && "opacity-50",
              labelClassName
            )}
          >
            {label}
            {isRequired && (
              <span className="text-custom-red-500 ml-1" aria-label="required">
                *
              </span>
            )}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Checkbox Options */}
        <div
          className={cn(
            "space-y-3",
            orientation === "horizontal" && "flex flex-wrap gap-6 space-y-0"
          )}
          role="group"
          aria-describedby={cn(
            descriptionId && descriptionId,
            messageId && messageId
          )}
        >
          {options.map((option, index) => (
            <div key={option.value} className="flex items-start space-x-2">
              <Checkbox
                id={`${fieldId}-${index}`}
                checked={value.includes(option.value)}
                onCheckedChange={(checked) =>
                  handleChange(option.value, checked as boolean)
                }
                disabled={disabled || option.disabled}
                className={cn(
                  sizes.checkbox,
                  validationState === "invalid" && "border-custom-red-500"
                )}
              />
              <div className="flex-1">
                <Label
                  htmlFor={`${fieldId}-${index}`}
                  className={cn(
                    sizes.label,
                    "font-normal cursor-pointer",
                    (disabled || option.disabled) &&
                      "opacity-50 cursor-not-allowed"
                  )}
                >
                  {option.label}
                </Label>
                {option.description && (
                  <p
                    className={cn(
                      sizes.description,
                      "text-muted-foreground mt-1"
                    )}
                  >
                    {option.description}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Messages */}
        {(error || warning || success) && (
          <div
            id={messageId}
            className={cn(
              sizes.message,
              "flex items-center gap-1",
              error && cn("text-custom-red-500", errorClassName),
              warning && cn("text-yellow-600", warningClassName),
              success && cn("text-green-600", successClassName)
            )}
            role="alert"
            aria-live="polite"
          >
            {error && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {warning && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {success && <CheckCircle className="h-3 w-3 flex-shrink-0" />}
            {error || warning || success}
          </div>
        )}
      </div>
    );
  }
);

CheckboxGroupField.displayName = "CheckboxGroupField";

// Single Checkbox Component
const CheckboxField = React.forwardRef<HTMLButtonElement, CheckboxFieldProps>(
  (
    {
      label,
      description,
      error,
      warning,
      success,
      required,
      disabled,
      className,
      labelClassName,
      descriptionClassName,
      errorClassName,
      warningClassName,
      successClassName,
      withAsterisk,
      size = "md",
      value = false,
      onChange,
      checkboxLabel,
      name,
      id,
    },
    ref
  ) => {
    const fieldId = id || React.useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const messageId =
      error || warning || success ? `${fieldId}-message` : undefined;

    const validationState = getValidationState(error, warning, success);
    const isRequired = required || withAsterisk;
    const sizes = sizeVariants[size];

    const handleChange = (checked: boolean) => {
      if (!disabled && onChange) {
        onChange(checked);
      }
    };

    return (
      <div className={cn("space-y-2", className)}>
        {/* Main Label */}
        {label && (
          <Label
            className={cn(
              sizes.label,
              "font-medium",
              validationState === "invalid" && "text-custom-red-500",
              disabled && "opacity-50",
              labelClassName
            )}
          >
            {label}
            {isRequired && (
              <span className="text-custom-red-500 ml-1" aria-label="required">
                *
              </span>
            )}
          </Label>
        )}

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(
              sizes.description,
              "text-muted-foreground",
              disabled && "opacity-50",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}

        {/* Checkbox */}
        <div className="flex items-start space-x-2">
          <Checkbox
            ref={ref}
            id={fieldId}
            name={name}
            checked={value}
            onCheckedChange={handleChange}
            disabled={disabled}
            className={cn(
              sizes.checkbox,
              validationState === "invalid" && "border-custom-red-500"
            )}
            aria-describedby={cn(
              descriptionId && descriptionId,
              messageId && messageId
            )}
          />
          {checkboxLabel && (
            <Label
              htmlFor={fieldId}
              className={cn(
                sizes.label,
                "font-normal cursor-pointer",
                disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              {checkboxLabel}
            </Label>
          )}
        </div>

        {/* Messages */}
        {(error || warning || success) && (
          <div
            id={messageId}
            className={cn(
              sizes.message,
              "flex items-center gap-1",
              error && cn("text-custom-red-500", errorClassName),
              warning && cn("text-yellow-600", warningClassName),
              success && cn("text-green-600", successClassName)
            )}
            role="alert"
            aria-live="polite"
          >
            {error && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {warning && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {success && <CheckCircle className="h-3 w-3 flex-shrink-0" />}
            {error || warning || success}
          </div>
        )}
      </div>
    );
  }
);

CheckboxField.displayName = "CheckboxField";

// Select Field Component
const SelectField = React.forwardRef<HTMLButtonElement, SelectFieldProps>(
  (
    {
      label,
      description,
      error,
      warning,
      success,
      required,
      disabled,
      className,
      warningClassName,
      successClassName,
      size = "md",
      options,
      value,
      onChange,
      placeholder,
      searchPlaceholder,
      emptyMessage,
      name,
      id,
    },
    ref
  ) => {
    const fieldId = id || React.useId();
    const messageId =
      error || warning || success ? `${fieldId}-message` : undefined;

    const validationState = getValidationState(error, warning, success);
    const sizes = sizeVariants[size];

    const handleValueChange = (newValue: string) => {
      if (!disabled && onChange) {
        onChange(newValue);
      }
    };

    // Convert our SelectOption[] to the format expected by your Select component
    const selectOptions = options.map((option) => ({
      value: option.value,
      label: option.label,
      disabled: option.disabled,
    }));

    // Determine error message for the Select component
    const errorMessage =
      error ||
      (validationState === "invalid" ? "Invalid selection" : undefined);
    const helperText = warning || success || description;

    return (
      <div className={cn("space-y-2", className)}>
        {/* Custom Select Component */}
        <Select
          ref={ref}
          id={fieldId}
          name={name}
          options={selectOptions}
          value={value || ""}
          onValueChange={handleValueChange}
          placeholder={placeholder}
          searchPlaceholder={searchPlaceholder}
          emptyMessage={emptyMessage}
          disabled={disabled}
          required={required}
          label={label}
          error={errorMessage}
          helperText={helperText}
          className={cn(
            // Size-based styling can be added here if needed
            validationState === "warning" && "border-yellow-500",
            validationState === "valid" && "border-green-500",
            className
          )}
        />

        {/* Additional Messages (if not handled by Select component) */}
        {(warning || success) && !description && (
          <div
            id={messageId}
            className={cn(
              sizes.message,
              "flex items-center gap-1",
              warning && cn("text-yellow-600", warningClassName),
              success && cn("text-green-600", successClassName)
            )}
            role="alert"
            aria-live="polite"
          >
            {warning && <AlertCircle className="h-3 w-3 flex-shrink-0" />}
            {success && <CheckCircle className="h-3 w-3 flex-shrink-0" />}
            {warning || success}
          </div>
        )}
      </div>
    );
  }
);

SelectField.displayName = "SelectField";

export {
  RadioGroupField,
  CheckboxGroupField,
  CheckboxField,
  SelectField,
  type RadioGroupFieldProps,
  type CheckboxGroupFieldProps,
  type CheckboxFieldProps,
  type SelectFieldProps,
  type RadioOption,
  type CheckboxOption,
  type SelectOption,
};

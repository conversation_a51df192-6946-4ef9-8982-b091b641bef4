"use client";

import React from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, User, Copy } from "lucide-react";
import { BlogDetailPost, BlogDetailsProps } from "../types";
import { BlogCard } from "@/features/blogs/components/BlogList";

// Default blog post data based on the diamond jewelry article
const defaultBlogPost: BlogDetailPost = {
  id: 1,
  title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
  excerpt:
    "Diamond jewelry in 2025 isn't just about sparkle—it's about making a statement that reflects your vibe, your story, and your style. From minimal pieces you'll wear daily to bold designs that turn heads at every event, diamonds this year are all about personal expression with a timeless edge.",
  image: "/placeholder.svg?height=400&width=800",
  category: "Diamond",
  date: "Dec 1, 2023",
  readTime: "5 min read",
  author: "<PERSON>",
  authorImage: "/placeholder.svg?height=60&width=60",
  authorBio:
    "<PERSON> is a jewelry expert with over 10 years of experience in diamond trends and luxury accessories.",
  content: `Diamond jewelry in 2025 isn't just about sparkle—it's about making a statement that reflects your vibe, your story, and your style. From minimal pieces you'll wear daily to bold designs that turn heads at every event, diamonds this year are all about personal expression with a timeless edge.`,
  sections: [
    {
      id: "minimalist-magic",
      title: "Minimalist Magic",
      content:
        "Simple, sleek, and totally wearable—minimalist diamond styles are dominating. Think delicate solitaire necklaces, thin diamond bands, and tiny studs you can wear 24/7. Paired with everyday outfits or layered with other pieces, these styles bring effortless elegance to your daily drip.",
      type: "text",
    },
    {
      id: "nature-inspired",
      title: "Nature-Inspired Pieces",
      content:
        "Florals and celestial shapes aren't going anywhere. Leafy diamond bracelets, star-shaped rings, and vine-inspired earrings bring a fresh, earthy feel to your collection. Nature meets sparkle = timeless beauty with a modern twist.",
      type: "text",
    },
    {
      id: "playful-colors",
      title: "Playful Pops of Color",
      content:
        "Who said diamonds are only white? Colored diamonds and gemstone-diamond combos are trending hard. Think blush pink diamonds, champagne tones, and pops of emerald, sapphire, or ruby alongside your classic stones. It's bold, fresh, and perfect for Gen Z & millennial brides (or anyone who loves fun luxe vibes).",
      type: "text",
    },
    {
      id: "layer-stack",
      title: "Layer It, Stack It, Love It",
      content:
        "Stacked diamond bands, layered necklaces, and ear party looks are still in. 2025 styling is all about mixing dainty and statement pieces together—because why wear one when you can wear five?",
      type: "text",
    },
    {
      id: "sustainable-sparkle",
      title: "Sustainable Sparkle",
      content:
        "Ethically sourced diamonds and lab-grown stones are shining in 2025. Brands are making sustainability luxe, giving you diamonds with a story you can feel proud of. Conscious luxury is the future.",
      type: "text",
    },
  ],
  tags: ["Diamond", "Jewelry", "Trends", "2025", "Fashion"],
  relatedPosts: [
    {
      id: 2,
      title:
        "The Ultimate Engagement Ring Guide: Choosing the Perfect Symbol of Love",
      excerpt:
        "Everything you need to know about selecting the perfect engagement ring...",
      image: "/placeholder.svg?height=200&width=300",
      category: "Engagement",
      date: "Nov 28, 2023",
      readTime: "7 min read",
    },
    {
      id: 3,
      title: "Lab-Grown vs Natural Diamonds: Making the Right Choice",
      excerpt:
        "Understanding the differences and benefits of lab-grown diamonds...",
      image: "/placeholder.svg?height=200&width=300",
      category: "Education",
      date: "Nov 25, 2023",
      readTime: "6 min read",
    },
    {
      id: 4,
      title: "Caring for Your Diamond Jewelry: Expert Tips",
      excerpt:
        "Keep your diamonds sparkling with these professional care tips...",
      image: "/placeholder.svg?height=200&width=300",
      category: "Care",
      date: "Nov 22, 2023",
      readTime: "4 min read",
    },
  ],
};

const BlogDetails: React.FC<BlogDetailsProps> = ({
  post = defaultBlogPost,
  onShare,
  onRelatedPostClick,
}) => {
  const handleShare = (platform: string) => {
    if (onShare) {
      onShare(platform, post.id);
    } else {
      // Default share behavior
      const url = window.location.href;
      const text = `Check out this article: ${post.title}`;

      switch (platform) {
        case "facebook":
          window.open(
            `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
              url
            )}`,
            "_blank"
          );
          break;
        case "twitter":
          window.open(
            `https://twitter.com/intent/tweet?text=${encodeURIComponent(
              text
            )}&url=${encodeURIComponent(url)}`,
            "_blank"
          );
          break;
        case "linkedin":
          window.open(
            `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
              url
            )}`,
            "_blank"
          );
          break;
        case "copy":
          navigator.clipboard.writeText(url);
          // You could show a toast notification here
          break;
      }
    }
  };

  const handleRelatedPostClick = (postId: number) => {
    if (onRelatedPostClick) {
      onRelatedPostClick(postId);
    } else {
      // Default navigation behavior
      console.log(`Navigate to post ${postId}`);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Featured Image */}
        <div className="mb-8">
          <div className="relative overflow-hidden rounded-lg">
            <Image
              src={post.image}
              alt={post.title}
              width={800}
              height={400}
              className="w-full h-64 md:h-96 object-cover"
              priority
            />
          </div>
        </div>
        {/* Header Section */}
        <div className="mb-4">
          {/* Category Badge */}
          <div className="mb-4">
            <Badge
              variant="secondary"
              className="bg-custom-blue-500 text-white hover:bg-custom-blue-600"
            >
              {post.category}
            </Badge>
          </div>

          {/* Title */}
          <h1 className="text-4xl md:text-3xl font-bold mb-2 leading-tight">
            {post.title}
          </h1>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>{post.date}</span>
            </div>
            {post.readTime && (
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{post.readTime}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span>By {post.author}</span>
            </div>
          </div>
        </div>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-12">
          {/* Introduction */}
          <div className="text-lg text-gray-700 mb-8 leading-relaxed">
            {post.content}
          </div>

          {/* Article Sections */}
          {post.sections?.map((section) => (
            <div key={section.id} className="mb-8">
              <h2 className="text-xl font-bold mb-4 text-gray-900">
                {section.title}
              </h2>

              {section.type === "text" && (
                <p className="text-gray-700 leading-relaxed mb-4">
                  {section.content}
                </p>
              )}

              {section.type === "image" && section.imageUrl && (
                <div className="mb-4">
                  <Image
                    src={section.imageUrl}
                    alt={section.title}
                    width={600}
                    height={300}
                    className="w-full rounded-lg"
                  />
                </div>
              )}

              {section.type === "quote" && (
                <blockquote className="border-l-4 border-custom-blue-500 pl-6 italic text-lg text-gray-600 mb-4">
                  {section.content}
                </blockquote>
              )}

              {section.type === "list" && section.listItems && (
                <ul className="list-disc list-inside space-y-2 text-gray-700 mb-4">
                  {section.listItems.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BlogDetails;

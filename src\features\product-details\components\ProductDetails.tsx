"use client";
import React from "react";
import SingleProduct, { ProductData } from "./SingleProduct";
import CustomerReviews from "./Reviews";

const ProductDetails = () => {
  // Example of how to use the dynamic SingleProduct component
  const handleAddToCart = (productId: string) => {
    console.log("Adding to cart:", productId);
    // Implement cart logic here
  };

  const handleAddToWishlist = (productId: string) => {
    console.log("Adding to wishlist:", productId);
    // Implement wishlist logic here
  };

  const handleFindStore = (productId: string) => {
    console.log("Finding store for:", productId);
    // Implement store finder logic here
  };

  const handleCustomize = (productId: string) => {
    console.log("Customizing product:", productId);
    // Implement customization logic here
  };

  const handleCopySku = (sku: string) => {
    console.log("SKU copied:", sku);
    // You could show a toast notification here
  };

  // Example of custom product data (optional - component will use defaults if not provided)
  const customProduct: ProductData = {
    id: "custom-earrings-001",
    title: "Elegant Diamond Studs",
    price: 95000,
    currency: "NPR",
    taxInfo: "Incl. taxes & Charges",
    category: "Women",
    description: [
      "Exquisite diamond stud earrings crafted with precision and elegance.",
      "Perfect for both casual and formal occasions, these timeless pieces will complement any outfit.",
    ],
    mainImage: {
      src: "/images/main-product.png",
      alt: "Elegant Diamond Studs",
      width: 600,
      height: 600,
    },
    thumbnailImages: [
      {
        src: "/placeholder.svg?height=200&width=200",
        alt: "Close-up view",
        width: 200,
        height: 200,
      },
      {
        src: "/placeholder.svg?height=200&width=200",
        alt: "Side view",
        width: 200,
        height: 200,
      },
    ],
    specifications: [
      { label: "Size", value: "6mm" },
      { label: "Gold", value: "18 KT White" },
      { label: "Diamond", value: "VVS - F" },
      { label: "Setting", value: "Prong" },
    ],
    sku: "EDS-001-18KW",
    detailDescription: "Set in 18 KT White Gold with premium VVS-F diamonds",
    materials: [
      {
        type: "gold",
        icon: "Au",
        bgColor: "bg-gray-50",
        iconBgColor: "bg-gray-400",
        weight: "Gross: 2.5 g",
        purity: "18 KT White",
      },
      {
        type: "diamond",
        icon: "♦",
        bgColor: "bg-blue-50",
        iconBgColor: "bg-blue-500",
        purity: "VVS - F",
        setting: "Prong Setting",
        total: 2,
        totalWeight: "0.50 ct",
      },
    ],
    isCustomizable: true,
    customizeButtonText: "CUSTOMIZE NOW",
  };

  return (
    <>
      <SingleProduct
        // product={customProduct} // Uncomment to use custom product data
        onAddToCart={handleAddToCart}
        onAddToWishlist={handleAddToWishlist}
        onFindStore={handleFindStore}
        onCustomize={handleCustomize}
        onCopySku={handleCopySku}
      />
      <CustomerReviews />
    </>
  );
};

export default ProductDetails;

<svg width="709" height="703" viewBox="0 0 709 703" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_361_4550)">
<ellipse cx="354.5" cy="351.5" rx="114.5" ry="111.5" fill="url(#paint0_linear_361_4550)" fill-opacity="0.5"/>
</g>
<defs>
<filter id="filter0_f_361_4550" x="0" y="0" width="709" height="703" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="120" result="effect1_foregroundBlur_361_4550"/>
</filter>
<linearGradient id="paint0_linear_361_4550" x1="354.5" y1="240" x2="354.5" y2="463" gradientUnits="userSpaceOnUse">
<stop stop-color="#D44FDB"/>
<stop offset="1" stop-color="#7D5AEF"/>
</linearGradient>
</defs>
</svg>

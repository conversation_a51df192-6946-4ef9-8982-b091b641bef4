"use client";

import { useState } from "react";
import { X, Heart, ChevronDown } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Select from "@/components/Select";
import Image from "next/image";

interface Product {
  id: number;
  name: string;
  price: number;
  category: string;
  image: string;
  badge: string;
}

interface Filter {
  id: string;
  label: string;
  type: "price" | "material" | "category";
}

const products: Product[] = [
  {
    id: 1,
    name: "Twight Twirl Diamond Ring",
    price: 12000,
    category: "EARRINGS",
    image: "/placeholder.svg?height=150&width=150",
    badge: "TR",
  },
  {
    id: 2,
    name: "Twight Twirl Diamond Ring",
    price: 12000,
    category: "EARRINGS",
    image: "/placeholder.svg?height=150&width=150",
    badge: "TR",
  },
  {
    id: 3,
    name: "Twight Twirl Diamond Ring",
    price: 12000,
    category: "EARRINGS",
    image: "/placeholder.svg?height=150&width=150",
    badge: "TR",
  },
  {
    id: 4,
    name: "Twight Twirl Diamond Ring",
    price: 12000,
    category: "EARRINGS",
    image: "/placeholder.svg?height=150&width=150",
    badge: "TR",
  },
  {
    id: 5,
    name: "Golden Circle Diamond Ring",
    price: 15000,
    category: "RINGS",
    image: "/placeholder.svg?height=150&width=150",
    badge: "TR",
  },
  {
    id: 6,
    name: "Silver Band Ring",
    price: 7500,
    category: "RINGS",
    image: "/placeholder.svg?height=150&width=150",
    badge: "TR",
  },
];

// Sort options for the Select component
const sortOptions = [
  { value: "featured", label: "Featured" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "name", label: "Name" },
];

// Category filter options
const categoryOptions = [
  { value: "", label: "All Categories" },
  { value: "earrings", label: "Earrings" },
  { value: "rings", label: "Rings" },
  { value: "necklaces", label: "Necklaces" },
  { value: "bracelets", label: "Bracelets" },
];

export function ProductList() {
  const [activeFilters, setActiveFilters] = useState<Filter[]>([
    { id: "price", label: "Under NPR 8,000", type: "price" },
    { id: "material", label: "Gold", type: "material" },
    { id: "category", label: "Rings", type: "category" },
  ]);
  const [sortBy, setSortBy] = useState("featured");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [favorites, setFavorites] = useState<number[]>([]);

  const removeFilter = (filterId: string) => {
    setActiveFilters((filters) => filters.filter((f) => f.id !== filterId));
  };

  const toggleFavorite = (productId: number) => {
    setFavorites((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  const filteredProducts = products.filter((product) => {
    const priceFilter = activeFilters.find((f) => f.type === "price");
    const materialFilter = activeFilters.find((f) => f.type === "material");
    const activeCategoryFilter = activeFilters.find(
      (f) => f.type === "category"
    );

    // Apply price filter
    if (priceFilter && product.price >= 8000) return false;

    // Apply active category filter from badges
    if (
      activeCategoryFilter &&
      !product.category
        .toLowerCase()
        .includes(activeCategoryFilter.label.toLowerCase())
    )
      return false;

    // Apply category filter from Select dropdown
    if (
      categoryFilter &&
      !product.category.toLowerCase().includes(categoryFilter.toLowerCase())
    )
      return false;

    return true;
  });

  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price;
      case "price-high":
        return b.price - a.price;
      case "name":
        return a.name.localeCompare(b.name);
      default:
        return 0;
    }
  });

  return (
    <div className="space-y-6 w-full">
      {/* Filters and Sort */}
      <div className="flex flex-col">
        {/* Active Filters and Sort */}
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          {/* Active Filters */}
          <div className="flex flex-wrap gap-2">
            {activeFilters.map((filter) => (
              <Badge
                key={filter.id}
                variant="secondary"
                className="px-3 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
              >
                {filter.label}
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2 h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => removeFilter(filter.id)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>

          {/* Sort Dropdown */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Sort By</span>
            <div className="w-48">
              <Select
                options={sortOptions}
                value={sortBy}
                onValueChange={setSortBy}
                placeholder="Select sort option"
                className="w-full"
              />
            </div>
          </div>
        </div>

        {/* Product Grid */}
        <div className="grid grid-cols-1 py-2 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {products.map((product, index) => (
            <div
              key={index.toString()}
              className="w-full group max-h-[302px] h-full rounded-md"
            >
              <div className="border relative rounded-md border-custom-violet-100">
                <Image
                  src={"/images/earrings.png"}
                  alt="alternate"
                  className="rounded-md"
                  width={1000}
                  height={1000}
                />
                <Heart className="absolute size-5 font-light top-2 right-2" />
              </div>
              <div className="p-2">
                <p className="text-sm line-clamp-1 text-custom-blue-500 uppercase">
                  {product.category}
                </p>
                <p className="text-lg text-custom-red-500">
                  NPR {product.price}
                </p>
                <p className="text-xs text-custom-red-500 line-clamp-1">
                  {product.name}
                </p>
              </div>
            </div>
          ))}
        </div>
        <div className="flex items-center justify-center py-1">
          <Button className="bg-custom-red-500 text-white hover:bg-custom-red-500 rounded-sm">
            Load More
          </Button>
        </div>

        {/* No Results */}
        {sortedProducts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              No products found matching your filters.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

"use client";

import React from "react";
import BlogDetails from "../components/BlogDetails";
import { BlogDetailPost } from "../types";

// Example of how to use the BlogDetails component with custom data
const BlogDetailsExample = () => {
  // Example 1: Custom blog post with different content types
  const customBlogPost: BlogDetailPost = {
    id: 2,
    title: "The Art of Diamond Cutting: From Rough to Radiant",
    excerpt: "Discover the intricate process that transforms rough diamonds into brilliant gems that capture light and hearts alike.",
    image: "/placeholder.svg?height=400&width=800",
    category: "Education",
    date: "Dec 5, 2023",
    readTime: "8 min read",
    author: "<PERSON>",
    authorImage: "/placeholder.svg?height=60&width=60",
    authorBio: "<PERSON> is a master diamond cutter with 25 years of experience in transforming rough diamonds into stunning gems.",
    content: "The journey from a rough diamond to a sparkling gem is one of the most fascinating processes in the jewelry world. It requires skill, precision, and an artistic eye that takes years to develop.",
    sections: [
      {
        id: "history",
        title: "A Brief History of Diamond Cutting",
        content: "Diamond cutting has evolved dramatically over the centuries. From the simple point cuts of the 14th century to today's precision-engineered brilliant cuts, each era has brought new techniques and understanding.",
        type: "text"
      },
      {
        id: "process",
        title: "The Cutting Process",
        content: "The transformation involves several critical steps:",
        type: "list",
        listItems: [
          "Planning: Analyzing the rough diamond's structure",
          "Cleaving: Splitting the diamond along natural grain lines",
          "Sawing: Using diamond-tipped saws for precise cuts",
          "Bruting: Shaping the diamond's girdle",
          "Polishing: Creating the final faceted surface"
        ]
      },
      {
        id: "quote",
        title: "Master's Wisdom",
        content: "A diamond cutter doesn't just shape stone; they release the light that has been waiting millions of years to shine.",
        type: "quote"
      },
      {
        id: "modern-tech",
        title: "Modern Technology in Diamond Cutting",
        content: "Today's diamond cutters use advanced laser technology and computer modeling to achieve unprecedented precision. These tools allow for cuts that maximize both beauty and value while minimizing waste.",
        type: "text"
      }
    ],
    tags: ["Diamond", "Cutting", "Craftsmanship", "Education", "Technology"],
    relatedPosts: [
      {
        id: 1,
        title: "Shine Bright: Trending Diamond Jewelry Styles in 2025",
        excerpt: "Discover the latest diamond jewelry trends...",
        image: "/placeholder.svg?height=200&width=300",
        category: "Trends",
        date: "Dec 1, 2023",
        readTime: "5 min read"
      }
    ]
  };

  // Custom handlers for sharing and navigation
  const handleShare = (platform: string, postId: number) => {
    console.log(`Sharing post ${postId} on ${platform}`);
    // Implement your custom sharing logic here
    // For example, you might track analytics, show custom share dialogs, etc.
  };

  const handleRelatedPostClick = (postId: number) => {
    console.log(`Navigating to post ${postId}`);
    // Implement your custom navigation logic here
    // For example, using Next.js router, updating state, etc.
  };

  return (
    <div>
      <h1 className="text-3xl font-bold mb-8 text-center">
        BlogDetails Component Examples
      </h1>
      
      {/* Example 1: Default usage */}
      <div className="mb-16">
        <h2 className="text-2xl font-semibold mb-4">Example 1: Default Usage</h2>
        <p className="text-gray-600 mb-8">
          Using the component with default data (diamond jewelry trends article):
        </p>
        <BlogDetails />
      </div>

      {/* Example 2: Custom data with handlers */}
      <div className="mb-16">
        <h2 className="text-2xl font-semibold mb-4">Example 2: Custom Data with Handlers</h2>
        <p className="text-gray-600 mb-8">
          Using the component with custom blog post data and event handlers:
        </p>
        <BlogDetails
          post={customBlogPost}
          onShare={handleShare}
          onRelatedPostClick={handleRelatedPostClick}
        />
      </div>
    </div>
  );
};

export default BlogDetailsExample;

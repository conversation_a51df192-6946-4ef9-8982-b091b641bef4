// "use client";
// import Image from "next/image";
// import React, { useRef } from "react";
// import Slider from "react-slick";
// import { secondarySectionSlidersettings } from "@/features/home/<USER>/home.obj";
// import { Button } from "@/components/ui/button";
// import { ChevronLeft, ChevronRight } from "lucide-react";
// import get from "lodash/get";
// const slickContent = [
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
//   {
//     img: "/images/jhumka.png",
//     price: `2000`,
//     description: "Lorem30 hey how you doing",
//   },
// ];
// const CollectionSlider = () => {
//   const sliderRef = useRef<Slider>(null);

//   const goToPrev = () => {
//     if (sliderRef.current) {
//       sliderRef.current.slickPrev();
//     }
//   };

//   const goToNext = () => {
//     if (sliderRef.current) {
//       sliderRef.current.slickNext();
//     }
//   };
//   return (
//     <section className="container mx-auto md:p-4 p-2 grid grid-cols-1 md:grid-cols-2 gap-y-4">
//       <div className="flex flex-col w-full py-6 justify-between">
//         <h2 className="font-lora text-sm md:text-base lg:text-[18px] pb-4">
//           Invest in Our Best Gold Collections
//         </h2>
//         <Slider ref={sliderRef} {...secondarySectionSlidersettings}>
//           {slickContent.map((item, index) => (
//             <div key={index}>
//               <Image
//                 src={item.img}
//                 alt="jhumka.png"
//                 className="rounded-sm border-1 border-custom-red-500"
//                 width={130}
//                 height={130}
//               />
//               <p className="text-custom-red-500 font-semibold text-lg">
//                 Price: {item.price}
//               </p>
//               <p className="text-xs line-clamp-1">
//                 {get(item, "description", "N/A")}
//               </p>
//             </div>
//           ))}
//         </Slider>
//         <div className="flex items-center justify-between py-2">
//           <div className="flex items-center gap-2">
//             <Button
//               variant="outline"
//               size="icon"
//               className=" bg-custom-red-500 border border-custom-red-500 backdrop-blur-sm hover:bg-custom-red-600 border-none rounded-full shadow-md"
//               onClick={goToPrev}
//             >
//               <ChevronLeft className="size-4 text-white" />
//             </Button>

//             <Button
//               variant="outline"
//               size="icon"
//               className="bg-custom-red-500 border border-custom-red-500 backdrop-blur-sm hover:bg-custom-red-600 border-none rounded-full shadow-md"
//               onClick={goToNext}
//             >
//               <ChevronRight className="size-4 text-white" />
//             </Button>
//           </div>
//           <Button className="bg-custom-red-500 hover:bg-custom-red-600 text-white">
//             Explore All Collections
//           </Button>
//         </div>
//       </div>
//     </section>
//   );
// };

// export default CollectionSlider;

// Second
"use client";
import Image from "next/image";
import React, { useRef } from "react";
import Slider from "react-slick";
import { secondarySectionSlidersettings } from "@/features/home/<USER>/home.obj";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import get from "lodash/get";

const slickContent = [
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
  {
    img: "/images/jhumka.png",
    price: `2000`,
    description: "Lorem30 hey how you doing",
  },
];

const CollectionSlider = () => {
  const sliderRef = useRef<Slider>(null);

  const goToPrev = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  return (
    <section className="w-full p-2 md:p-4">
      <div className="flex flex-col w-full py-6 justify-between">
        <h2 className="font-lora text-sm md:text-base lg:text-[18px] pb-4 font-medium">
          Invest in Our Best Gold Collections
        </h2>
        <div className="w-full">
          <Slider ref={sliderRef} {...secondarySectionSlidersettings}>
            {slickContent.map((item, index) => (
              <div key={index} className="px-2">
                <Image
                  src={item.img}
                  alt="jhumka.png"
                  className="rounded-sm border-1 border-custom-red-500"
                  width={0} // Set to 0 to use layout="responsive"
                  height={0} // Set to 0 to use layout="responsive"
                  sizes="100vw" // Ensures responsive scaling
                  style={{ width: "100%", height: "auto" }} // Makes image responsive
                />
                <p className="text-custom-red-500 font-semibold text-lg">
                  Price: {item.price}
                </p>
                <p className="text-xs line-clamp-1">
                  {get(item, "description", "N/A")}
                </p>
              </div>
            ))}
          </Slider>
        </div>
        <div className="flex items-center justify-between py-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="bg-custom-red-500 border border-custom-red-500 backdrop-blur-sm hover:bg-custom-red-600 border-none rounded-full shadow-md"
              onClick={goToPrev}
            >
              <ChevronLeft className="size-4 text-white" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="bg-custom-red-500 border border-custom-red-500 backdrop-blur-sm hover:bg-custom-red-600 border-none rounded-full shadow-md"
              onClick={goToNext}
            >
              <ChevronRight className="size-4 text-white" />
            </Button>
          </div>
          <Button className="bg-custom-red-500 hover:bg-custom-red-600 text-white">
            Explore All Collections
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CollectionSlider;

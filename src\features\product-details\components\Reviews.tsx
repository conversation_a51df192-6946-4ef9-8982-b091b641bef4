"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { InputField, TextareaField } from "@/components/ui/form-field";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Star, User, Mail, MessageSquare } from "lucide-react";

// Form validation schema (using basic validation for now)
interface ReviewFormData {
  name: string;
  email: string;
  rating: number;
  title: string;
  comment: string;
}

// Validation rules
const validateForm = (data: ReviewFormData) => {
  const errors: Partial<Record<keyof ReviewFormData, string>> = {};

  if (!data.name.trim()) {
    errors.name = "Name is required";
  } else if (data.name.trim().length < 2) {
    errors.name = "Name must be at least 2 characters";
  }

  if (!data.email.trim()) {
    errors.email = "Email is required";
  } else if (!/\S+@\S+\.\S+/.test(data.email)) {
    errors.email = "Please enter a valid email address";
  }

  if (data.rating === 0) {
    errors.rating = "Please select a rating";
  }

  if (!data.title.trim()) {
    errors.title = "Review title is required";
  } else if (data.title.trim().length < 5) {
    errors.title = "Title must be at least 5 characters";
  }

  if (!data.comment.trim()) {
    errors.comment = "Review comment is required";
  } else if (data.comment.trim().length < 10) {
    errors.comment = "Comment must be at least 10 characters";
  } else if (data.comment.trim().length > 1000) {
    errors.comment = "Comment must be less than 1000 characters";
  }

  return errors;
};

interface Review {
  id: number;
  name: string;
  avatar?: string;
  rating: number;
  date: string;
  comment: string;
}

interface RatingBreakdown {
  stars: number;
  count: number;
  percentage: number;
}

export default function CustomerReviews() {
  const [hoveredStar, setHoveredStar] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid },
  } = useForm<ReviewFormData>({
    defaultValues: {
      name: "",
      email: "",
      rating: 0,
      title: "",
      comment: "",
    },
    mode: "onChange",
  });

  const watchedRating = watch("rating");
  const watchedComment = watch("comment");

  // Sample data - in a real app this would come from an API
  const reviews: Review[] = [
    {
      id: 1,
      name: "Ava Rodriguez",
      rating: 5,
      date: "Dec 15, 2023",
      comment:
        "Quality is good though, I use it on face too and it's not rough. It's suitable for all skin type. Does not too harsh and work is very good.",
    },
    {
      id: 2,
      name: "Isabella K. Garcia",
      rating: 4,
      date: "Dec 10, 2023",
      comment:
        "Quality is good though, I use it on face too and it's not rough. It's suitable for all skin type. Does not too harsh and work is very good.",
    },
    {
      id: 3,
      name: "Charlotte Wilson",
      rating: 3,
      date: "Dec 8, 2023",
      comment:
        "Quality is good though, I use it on face too and it's not rough. It's suitable for all skin type. Does not too harsh and work is very good.",
    },
  ];

  const ratingBreakdown: RatingBreakdown[] = [
    { stars: 5, count: 3, percentage: 60 },
    { stars: 4, count: 1, percentage: 20 },
    { stars: 3, count: 1, percentage: 20 },
    { stars: 2, count: 0, percentage: 0 },
    { stars: 1, count: 0, percentage: 0 },
  ];

  const totalReviews = reviews.length;
  const averageRating =
    reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;

  const renderStars = (
    rating: number,
    interactive = false,
    size = "w-4 h-4",
    onStarClick?: (rating: number) => void
  ) => {
    return Array.from({ length: 5 }, (_, index) => {
      const starNumber = index + 1;
      const isFilled = interactive
        ? starNumber <= (hoveredStar || watchedRating)
        : starNumber <= rating;

      return (
        <Star
          key={index}
          className={`${size} ${
            isFilled ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
          } ${interactive ? "cursor-pointer hover:text-yellow-400" : ""}`}
          onClick={
            interactive && onStarClick
              ? () => onStarClick(starNumber)
              : undefined
          }
          onMouseEnter={
            interactive ? () => setHoveredStar(starNumber) : undefined
          }
          onMouseLeave={interactive ? () => setHoveredStar(0) : undefined}
        />
      );
    });
  };

  const onSubmit = async (data: ReviewFormData) => {
    setIsSubmitting(true);

    try {
      // Validate the form data
      const validationErrors = validateForm(data);
      if (Object.keys(validationErrors).length > 0) {
        console.log("Validation errors:", validationErrors);
        return;
      }

      // In a real app, this would submit to an API
      console.log("Submitting review:", data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Reset form on success
      reset();
      setHoveredStar(0);
      alert("Review submitted successfully!");
    } catch (error) {
      console.error("Error submitting review:", error);
      alert("Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container md:px-4 px-2 mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">
          Customer Reviews ({totalReviews})
        </h2>
      </div>

      {/* Rating Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Overall Rating */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="text-4xl font-bold">
                  {averageRating.toFixed(1)}
                </div>
                <div className="flex items-center gap-1">
                  {renderStars(averageRating)}
                </div>
                <div className="text-sm text-gray-500">
                  ({totalReviews} reviews)
                </div>
              </div>
            </div>

            {/* Rating Breakdown */}
            <div className="space-y-2">
              {ratingBreakdown.map((item) => (
                <div key={item.stars} className="flex items-center gap-3">
                  <div className="flex items-center gap-1 w-12">
                    <span className="text-sm">{item.stars}</span>
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  </div>
                  <Progress value={item.percentage} className="flex-1 h-2" />
                  <span className="text-sm text-gray-500 w-8">
                    {item.percentage}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Reviews */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="flex gap-4">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={review.avatar || "/placeholder.svg"} />
                  <AvatarFallback>
                    <User className="w-5 h-5" />
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold">{review.name}</h4>
                    <span className="text-sm text-gray-500">{review.date}</span>
                  </div>

                  <div className="flex items-center gap-1">
                    {renderStars(review.rating)}
                  </div>

                  <p className="text-gray-700 leading-relaxed">
                    {review.comment}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Write a Review */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl text-custom-red-500">
            Write a Review
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Share your experience with this product to help other customers
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Customer Information */}
            <div className="grid md:grid-cols-2 gap-6">
              <Controller
                name="name"
                control={control}
                rules={{
                  required: "Name is required",
                  minLength: {
                    value: 2,
                    message: "Name must be at least 2 characters",
                  },
                }}
                render={({ field }) => (
                  <InputField
                    {...field}
                    label="Your Name"
                    placeholder="Enter your full name"
                    error={errors.name?.message}
                    required
                    leftSection={<User className="h-4 w-4" />}
                    description="This will be displayed with your review"
                  />
                )}
              />

              <Controller
                name="email"
                control={control}
                rules={{
                  required: "Email is required",
                  pattern: {
                    value: /\S+@\S+\.\S+/,
                    message: "Please enter a valid email address",
                  },
                }}
                render={({ field }) => (
                  <InputField
                    {...field}
                    label="Email Address"
                    type="email"
                    placeholder="Enter your email"
                    error={errors.email?.message}
                    required
                    leftSection={<Mail className="h-4 w-4" />}
                    description="We'll never share your email publicly"
                  />
                )}
              />
            </div>

            {/* Rating */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Your overall rating of this product{" "}
                <span className="text-custom-red-500">*</span>
              </label>
              <div className="flex items-center gap-2">
                {renderStars(watchedRating, true, "w-8 h-8", (rating) =>
                  setValue("rating", rating, { shouldValidate: true })
                )}
                {watchedRating > 0 && (
                  <span className="text-sm text-muted-foreground ml-2">
                    ({watchedRating} star{watchedRating !== 1 ? "s" : ""})
                  </span>
                )}
              </div>
              {errors.rating && (
                <p className="text-xs text-custom-red-500 flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {errors.rating.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground">
                Click on the stars to rate this product
              </p>
            </div>

            {/* Review Title */}
            <Controller
              name="title"
              control={control}
              rules={{
                required: "Review title is required",
                minLength: {
                  value: 5,
                  message: "Title must be at least 5 characters",
                },
                maxLength: {
                  value: 100,
                  message: "Title must be less than 100 characters",
                },
              }}
              render={({ field }) => (
                <InputField
                  {...field}
                  label="Review Title"
                  placeholder="Summarize your experience in a few words"
                  error={errors.title?.message}
                  required
                  leftSection={<MessageSquare className="h-4 w-4" />}
                  description="Give your review a helpful title"
                />
              )}
            />

            {/* Review Comment */}
            <Controller
              name="comment"
              control={control}
              rules={{
                required: "Review comment is required",
                minLength: {
                  value: 10,
                  message: "Comment must be at least 10 characters",
                },
                maxLength: {
                  value: 1000,
                  message: "Comment must be less than 1000 characters",
                },
              }}
              render={({ field }) => (
                <TextareaField
                  {...field}
                  label="Your Review"
                  placeholder="Share your detailed experience with this product..."
                  error={errors.comment?.message}
                  required
                  rows={5}
                  resize="vertical"
                  description={`${
                    watchedComment?.length || 0
                  }/1000 characters - Be specific and helpful`}
                />
              )}
            />

            {/* Submit Button */}
            <div className="flex gap-4">
              <Button
                type="submit"
                disabled={!isValid || isSubmitting}
                loading={isSubmitting}
                loadingText="Submitting..."
                className="bg-custom-red-500 hover:bg-custom-red-600"
              >
                Submit Review
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  reset();
                  setHoveredStar(0);
                }}
                disabled={isSubmitting}
              >
                Clear Form
              </Button>
            </div>

            {/* Form Status */}
            {!isValid && Object.keys(errors).length > 0 && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-custom-red-500 font-medium">
                  Please fix the following errors:
                </p>
                <ul className="mt-2 text-xs text-custom-red-500 list-disc list-inside">
                  {Object.entries(errors).map(([field, error]) => (
                    <li key={field}>{error?.message}</li>
                  ))}
                </ul>
              </div>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

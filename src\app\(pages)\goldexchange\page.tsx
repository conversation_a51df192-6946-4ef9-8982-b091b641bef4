import FrequentlyAskedSection from "@/components/shared/FrequentyAsked/FrequentlyAskedSection";
import Join<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/shared/JoinRidhiSidhi";
import { CategoriesSliderData } from "@/features/aboutus/components/aboutObj";
import Categories from "@/features/aboutus/components/Categories";
import GoldValueSection from "@/features/goldexchange/components/GoldValueSection";
import HeroSection from "@/features/goldexchange/components/HeroSection";
import React from "react";

const page = () => {
  return (
    <div>
      <HeroSection />
      <FrequentlyAskedSection />
      <GoldValueSection />
      <Categories categories={CategoriesSliderData} />
      <JoinRidhiSidhi />
    </div>
  );
};

export default page;

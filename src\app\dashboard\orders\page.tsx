import { Card, CardContent } from "@/components/ui/card";
import { get } from "lodash";
import Image from "next/image";
import React from "react";

const obj = [
  { title: "hello", description: "Enticing Petite Drop Earrings", price: 200 },
];
const page = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {obj.map((item, index) => (
        <Card key={index} className="p-4">
          <CardContent className="flex items-center gap-4 p-0">
            <div className="aspect-square relative size-20 bg-gray-100 rounded-lg overflow-hidden">
              <Image
                src={"/images/ring.png"}
                alt={"ring"}
                fill
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            <div>
              <p className="font-semibold text-lg">NPR {item.price}</p>
              <p className="text-base text-custom-red-500  line-clamp-1">
                {get(item, "description", "N/A")}
              </p>
              <div className="flex text-sm items-center gap-4">
                <span>Size: 12</span>
                <span>Qty: 1</span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default page;

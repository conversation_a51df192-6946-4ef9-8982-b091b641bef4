"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Facebook, Instagram, Mail, MessageCircle, Phone } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function Footer() {
  const [hoveredLink, setHoveredLink] = useState<string | null>(null);

  const footerSections = [
    {
      title: "Know Your Jewellery",
      links: [
        { name: "Diamond Guide", href: "/diamond-guide" },
        { name: "Jewellery Guide", href: "/jewellery-guide" },
        { name: "Gold Rate", href: "/gold-rate" },
        { name: "Treasure chest", href: "/treasure-chest" },
      ],
    },
    {
      title: "Shree Ridhi Sidhi Jewellers",
      links: [
        { name: "15-day returns", href: "/returns" },
        { name: "Gold Exchange", href: "/gold-exchange" },
      ],
    },
    {
      title: "Customer Policy",
      links: [
        { name: "Return Policy", href: "/return-policy" },
        { name: "Order Status", href: "/order-status" },
      ],
    },
    {
      title: "About Us",
      links: [
        { name: "Our Story", href: "/our-story" },
        { name: "Blog", href: "/blog" },
      ],
    },
  ];

  const contactInfo = {
    businessName: "Shree Ridhi Sidhi Jewellers",
    address: "Newroad, Bishal Bazaar, Kathmandu, Nepal",
    mobile: "970-9043726",
    whatsapp: "977 984-0253095",
    email: "<EMAIL>",
  };

  const socialLinks = [
    {
      name: "Instagram",
      icon: Instagram,
      href: "https://instagram.com",
      color: "hover:text-pink-600",
    },
    {
      name: "Facebook",
      icon: Facebook,
      href: "https://facebook.com",
      color: "hover:text-blue-600",
    },
  ];

  const enquirySupport = [
    {
      name: "Call us",
      icon: Phone,
      href: `tel:${contactInfo.mobile}`,
      color: "hover:text-green-600",
    },
    {
      name: "WhatsApp",
      icon: MessageCircle,
      href: `https://wa.me/${contactInfo.whatsapp.replace(/\s/g, "")}`,
      color: "hover:text-green-500",
    },
    {
      name: "Email",
      icon: Mail,
      href: `mailto:${contactInfo.email}`,
      color: "hover:text-custom-red-500",
    },
  ];

  return (
    <footer className="bg-custom-yellow-50  px-4">
      <div className="">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 py-8">
          {/* Footer Sections */}
          {footerSections.map((section, index) => (
            <div key={index} className="space-y-4">
              <h3 className="text-custom-red-500 font-semibold text-lg mb-4">
                {section.title}
              </h3>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-gray-700 hover:text-custom-red-500 transition-colors duration-200 text-sm"
                      onMouseEnter={() =>
                        setHoveredLink(`${index}-${linkIndex}`)
                      }
                      onMouseLeave={() => setHoveredLink(null)}
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* Contact Information */}
          <div className="space-y-4 lg:col-span-1">
            <h3 className="text-custom-red-500 font-semibold text-lg mb-4">
              Contact Us
            </h3>
            <div className="space-y-3 text-sm text-gray-700">
              <p className="font-medium">{contactInfo.businessName}</p>
              <p className="leading-relaxed">{contactInfo.address}</p>
              <div className="space-y-1">
                <p>
                  <span className="font-medium">Mobile Number :</span>{" "}
                  <Link
                    href={`tel:${contactInfo.mobile}`}
                    className="hover:text-custom-red-500 transition-colors"
                  >
                    {contactInfo.mobile}
                  </Link>
                </p>
                <p>
                  <span className="font-medium">What's App :</span>{" "}
                  <Link
                    href={`https://wa.me/${contactInfo.whatsapp.replace(
                      /\s/g,
                      ""
                    )}`}
                    className="hover:text-custom-red-500 transition-colors"
                  >
                    {contactInfo.whatsapp}
                  </Link>
                </p>
                <p>
                  <span className="font-medium">Email :</span>{" "}
                  <Link
                    href={`mailto:${contactInfo.email}`}
                    className="hover:text-custom-red-500 transition-colors"
                  >
                    {contactInfo.email}
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center py-6 border-t border-orange-200">
          {/* Social Media */}
          <div className="mb-6 md:mb-0">
            <h4 className="text-custom-red-500 font-semibold mb-3">
              Find Us on
            </h4>
            <div className="flex space-x-3">
              {socialLinks.map((social, index) => {
                const IconComponent = social.icon;
                return (
                  <Button
                    key={index}
                    variant="outline"
                    size="icon"
                    className={`border-red-200 text-custom-red-500 ${social.color} transition-all duration-200 hover:scale-110`}
                    onClick={() => window.open(social.href, "_blank")}
                  >
                    <IconComponent className="w-5 h-5" />
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Enquiry Support */}
          <div>
            <h4 className="text-custom-red-500 font-semibold mb-3">
              ENQUIRY SUPPORT
            </h4>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
              {enquirySupport.map((support, index) => {
                const IconComponent = support.icon;
                return (
                  <Button
                    key={index}
                    variant="ghost"
                    leftIcon={<IconComponent className="size-4" />}
                    className={`flex items-center space-x-1 text-custom-red-500 ${support.color} transition-all duration-200 hover:scale-105 justify-start sm:justify-center`}
                    onClick={() => {
                      if (support.href.startsWith("http")) {
                        window.open(support.href, "_blank");
                      } else {
                        window.location.href = support.href;
                      }
                    }}
                  >
                    <span className="text-sm font-medium">{support.name}</span>
                  </Button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center py-4 border-t border-orange-200">
          <p className="text-gray-600 text-sm">
            © 2024 Shree Ridhi Sidhi Jewellers. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}

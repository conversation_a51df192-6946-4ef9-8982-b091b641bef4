// Blog post interface
export interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  image: string;
  category: string;
  date: string;
  readTime?: string;
  slug?: string;
  author?: string;
  tags?: string[];
  content?: string;
}

// Recent posts interface
export interface RecentPost {
  id: number;
  title: string;
  date: string;
  slug?: string;
}

// Blog category interface
export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  count?: number;
}

// Blog tag interface
export interface BlogTag {
  id: number;
  name: string;
  slug: string;
  count?: number;
}

// Social media link interface
export interface SocialLink {
  name: string;
  icon: React.ComponentType<any>;
  href: string;
  color: string;
}

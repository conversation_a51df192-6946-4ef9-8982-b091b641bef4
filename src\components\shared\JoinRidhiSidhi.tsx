// import Image from "next/image";
// import React from "react";
// import { Input } from "../ui/input";

// const JoinRidhiSidhi = () => {
//   return (
//     // <div className="py-4 px-16 mx-10 bg-gradient-to-b from-custom-violet-900 to-custom-blue-500 flex items-center justify-between rounded-2xl ">
//     <div className="py-10 px-[10rem] mx-10 bg-[linear-gradient(to_bottom,_#1c1225_0%,_#1c1225_50%,_#7d5aef_80%,_#7d5aef_100%)] flex flex-col md:flex-row items-center justify-between rounded-2xl">
//       <div className="flex flex-col md:flex-row items-center gap-4 w-full md:w-[60%]">
//         <Image
//           src="/aboutpage/DiamondRing.png"
//           alt="Diamond Ring"
//           height={100}
//           width={100}
//         />
//         <div className="flex flex-col font-lora">
//           <h2 className="font-semibold text-lg md:text-xl text-white lg:text-2xl">
//             Join Shree Ridhi Sidhi Jewellers
//           </h2>
//           <p className="text-sm text-white">
//             Discover new designs, trending styles and more.
//           </p>
//         </div>
//       </div>

//       <div className="flex flex-col gap-2 w-full md:w-[40%]">
//         <Input
//           placeholder="Enter Email"
//           className="bg-white py-3 px-5 rounded-xl h-full w-full "
//         />
//         <button className="font-lora text-white text-sm lg:text-base bg-custom-vibrant-magneta-purple px-5 py-2 rounded-xl h-full  ">
//           Submit
//         </button>
//       </div>
//     </div>
//   );
// };

// export default JoinRidhiSidhi;

// Second

import Image from "next/image";
import React from "react";
import { Input } from "../ui/input";

const JoinRidhiSidhi = () => {
  return (
    <div className="py-6 px-4 sm:px-8 md:px-16 lg:px-24 xl:px-32 mx-4 sm:mx-6 md:mx-10 bg-[linear-gradient(to_bottom,_#1c1225_0%,_#1c1225_50%,_#7d5aef_80%,_#7d5aef_100%)] flex flex-col md:flex-row items-center justify-between rounded-2xl gap-6 md:gap-8">
      <div className="flex flex-col md:flex-row items-center gap-4 w-full md:w-3/5 lg:w-2/3">
        <Image
          src="/aboutpage/DiamondRing.png"
          alt="Diamond Ring"
          height={80}
          width={80}
          className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 object-contain"
        />
        <div className="flex flex-col font-lora text-center md:text-left">
          <h2 className="font-semibold text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl text-white">
            Join Shree Ridhi Sidhi Jewellers
          </h2>
          <p className="text-xs sm:text-sm  text-white mt-1 sm:mt-2">
            Discover new designs, trending styles and more.
          </p>
        </div>
      </div>

      <div className="flex flex-col gap-3 sm:gap-4 w-full md:w-2/5 lg:w-1/3 mt-4 md:mt-0">
        <Input
          placeholder="Enter Email"
          className="bg-white py-2 px-4 sm:py-3 sm:px-5  rounded-xl text-sm sm:text-base h-full w-full"
        />
        <button className="font-lora text-white text-sm bg-custom-vibrant-magneta-purple px-4 py-2 sm:px-5 sm:py-2 rounded-xl w-full hover:bg-opacity-90 transition-colors h-full cursor-pointer">
          Submit
        </button>
      </div>
    </div>
  );
};

export default JoinRidhiSidhi;

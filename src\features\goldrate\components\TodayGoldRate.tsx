// import {
//   EllipseLeft,
//   EllipseRight,
// } from "@/features/aboutus/components/SvgComponent";
// import React from "react";

// const TodayGoldRate = () => {
//   return (
//     <div className="p-4 h-full w-full overflow-hidden">
//       <div className="font-lora flex flex-col items-center justify-center ">
//         <h1 className=" text-xl md:text-2xl lg:text-[28px] font-semibold">
//           Today's Gold Rate in Nepal
//         </h1>
//         <p className="text-custom-textgray text-xs md:text-sm lg:text-base max-w-[57rem]">
//           Stay updated with the latest gold prices to make confident, informed
//           purchases. We update our rates regularly to reflect the live market
//           trends.
//         </p>
//       </div>
//       <div className="absolute bottom-0 left-0 transform translate-y-1/2 -translate-x-1/4">
//         <EllipseLeft />
//       </div>
//       {/* Top Right Ellipse */}

//       <div className="absolute top-0 right-0 transform -translate-y-1/2 translate-x-1/4">
//         <EllipseRight />
//       </div>
//     </div>
//   );
// };

// export default TodayGoldRate;

// Second

import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import React from "react";

const TodayGoldRate = () => {
  return (
    <div className="relative p-4 h-full w-full overflow-hidden">
      <div className="font-lora flex flex-col items-center justify-center ">
        <h1 className=" text-xl md:text-2xl lg:text-[28px] font-semibold">
          Today's Gold Rate in Nepal
        </h1>
        <p className="text-custom-textgray text-xs md:text-sm lg:text-base max-w-[57rem]">
          Stay updated with the latest gold prices to make confident, informed
          purchases. We update our rates regularly to reflect the live market
          trends.
        </p>
      </div>
      <div className="absolute bottom-48 left-3 transform translate-y-1/2 -translate-x-1/4">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute -top-20 -right-30 transform -translate-y-1/2 translate-x-1/4">
        <EllipseRight />
      </div>
    </div>
  );
};

export default TodayGoldRate;

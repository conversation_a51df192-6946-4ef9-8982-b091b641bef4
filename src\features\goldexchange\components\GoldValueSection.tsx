"use client";
import React, { useState } from "react";

const GoldValueSection = () => {
  const [activeTab, setActiveTab] = useState<"karatage" | "purity">("karatage");
  const [selectedKaratage, setSelectedKaratage] = useState("");
  const [selectedPurity, setSelectedPurity] = useState("");
  const [grams, setGrams] = useState("");

  const karatageOptions = ["24K", "22K", "21K", "18K", "14K", "10K"];
  const purityOptions = ["99.9%", "91.6%", "87.5%", "75%", "58.3%", "41.7%"];

  return (
    <div className="py-4 px-4 container mx-auto md:px-14 lg:px-16 flex flex-col md:flex-row items-center gap-8 bg-[linear-gradient(94deg,_#D1D1D11E_0.43%,_#D44FDB0A_100%)] font-lora">
      <div className="space-y-4 w-full md:w-1/2">
        <h2 className=" text-xl md:text-2xl lg:text-[28px] font-semibold">
          Estimate Your Gold's Value
        </h2>
        <p className=" text-xs md:text-sm lg:text-base">
          Check the current gold rate we use to evaluate your old jewellery for
          exchange.
        </p>

        <div className="border border-gray-200 rounded-xl shadow-md  p-8 ">
          <div className="flex items-center justify-between my-2">
            <h3 className="text-xs lg:text-sm">Purity</h3>
            <h3 className="text-xs lg:text-sm">Today's Standard Gold Rate</h3>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-xs lg:text-sm">24 KT Gold (1 gram)</p>
            <p className="text-xs lg:text-sm">NPR 10,068</p>
          </div>
        </div>
        <p className="text-xs text-custom-textgray italic">
          *Exchange value calculated at 24KT rate. Final valuation based on
          purity and weight.
        </p>
      </div>

      <div className="w-full md:w-1/2">
        {/* Tab Navigation */}
        <div className="flex rounded-lg bg-gray-100 p-1 mb-6">
          <button
            onClick={() => setActiveTab("karatage")}
            className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-colors ${
              activeTab === "karatage"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            Karatage
          </button>
          <button
            onClick={() => setActiveTab("purity")}
            className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-colors ${
              activeTab === "purity"
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            Purity
          </button>
        </div>

        {/* Tab Content */}
        <div className="space-y-4">
          {/* Dropdown */}
          <div className="relative">
            <select
              value={
                activeTab === "karatage" ? selectedKaratage : selectedPurity
              }
              onChange={(e) =>
                activeTab === "karatage"
                  ? setSelectedKaratage(e.target.value)
                  : setSelectedPurity(e.target.value)
              }
              className="w-full py-3 px-4 border border-gray-300 rounded-lg bg-white text-gray-500 focus:outline-none focus:ring-2 focus:ring-custom-red-500 focus:border-transparent appearance-none"
            >
              <option value="">
                {activeTab === "karatage" ? "Select Karatage" : "Select Purity"}
              </option>
              {(activeTab === "karatage" ? karatageOptions : purityOptions).map(
                (option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                )
              )}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg
                className="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          </div>

          {/* Grams Input */}
          <div>
            <input
              type="text"
              value={grams}
              onChange={(e) => setGrams(e.target.value)}
              placeholder="Enter Grams"
              className="w-full py-3 px-4 border border-gray-300 rounded-lg bg-white text-gray-500 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-custom-red-500 focus:border-transparent"
            />
          </div>

          {/* Calculate Button */}
          <button className="w-full py-3 px-4 bg-custom-red-500 text-white font-medium rounded-lg hover:bg-custom-red-600 transition-colors">
            Calculate Exchange Value
          </button>

          {/* Disclaimer */}
          <p className="text-xs text-gray-500 italic">
            *Final exchange price will be confirmed after purity verification at
            our store.
          </p>
        </div>
      </div>
    </div>
  );
};

export default GoldValueSection;

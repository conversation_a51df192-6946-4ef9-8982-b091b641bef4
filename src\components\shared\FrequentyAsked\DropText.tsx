// "use client";
// import React, { useState } from "react";
// import { FaChevronDown } from "react-icons/fa"; // Using Font Awesome chevron, adjust import as needed

// interface DropdownProps {
//   title: string;
//   description: string;
// }

// const DropText: React.FC<DropdownProps> = ({ title, description }) => {
//   const [isOpen, setIsOpen] = useState(false);

//   return (
//     <div className="w-full max-w-md mx-auto bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
//       <button
//         type="button"
//         onClick={() => setIsOpen(!isOpen)}
//         className="w-full px-4 py-3 text-left text-gray-800 font-medium focus:outline-none flex justify-between items-center"
//       >
//         <span>{title}</span>
//         <FaChevronDown
//           className={`w-5 h-5 transition-transform ${
//             isOpen ? "rotate-180" : ""
//           }`}
//         />
//       </button>
//       <div
//         className={`px-4 py-2 text-gray-600 bg-gray-50 overflow-hidden transition-all duration-300 ease-in-out`}
//         style={{
//           maxHeight: isOpen ? "100px" : "0",
//           opacity: isOpen ? 1 : 0,
//         }}
//       >
//         <p className="text-sm">{description}</p>
//       </div>
//     </div>
//   );
// };

// export default DropText;

// "use client";
// import React, { useState } from "react";
// import { FaChevronDown } from "react-icons/fa"; // Using Font Awesome chevron, adjust import as needed

// interface DropdownProps {
//   title: string;
//   description: string;
// }

// const DropText: React.FC<DropdownProps> = ({ title, description }) => {
//   const [isOpen, setIsOpen] = useState(false);

//   return (
//     <div className="w-full max-w-md mx-auto bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
//       <button
//         type="button"
//         onClick={() => setIsOpen(!isOpen)}
//         className="w-full px-4 py-3 text-left text-gray-800 font-medium focus:outline-none flex justify-between items-center"
//       >
//         <span>{title}</span>
//         <FaChevronDown
//           className={`w-5 h-5 transition-transform duration-300 ease-in-out ${
//             isOpen ? "rotate-180" : ""
//           }`}
//         />
//       </button>
//       <div
//         className={`px-4 py-2 text-gray-600 bg-gray-50 overflow-hidden transition-all duration-500 ease-in-out`}
//         style={{
//           maxHeight: isOpen ? "200px" : "0",
//           opacity: isOpen ? 1 : 0,
//           transitionProperty: "max-height, opacity",
//           transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
//         }}
//       >
//         <p className="text-sm">{description}</p>
//       </div>
//     </div>
//   );
// };

// export default DropText;

// "use client";
// import React, { useState } from "react";
// import { FaChevronDown } from "react-icons/fa"; // Using Font Awesome chevron, adjust import as needed

// interface DropdownProps {
//   title: string;
//   description: string;
// }

// const DropText: React.FC<DropdownProps> = ({ title, description }) => {
//   const [isOpen, setIsOpen] = useState(false);

//   return (
//     <div className="w-full max-w-md mx-auto bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
//       <button
//         type="button"
//         onClick={() => setIsOpen(!isOpen)}
//         className="w-full px-4 py-3 text-left text-gray-800 font-medium focus:outline-none flex justify-between items-center"
//       >
//         <span>{title}</span>
//         <FaChevronDown
//           className={`w-5 h-5 transition-transform duration-300 ease-in-out ${
//             isOpen ? "rotate-180" : ""
//           }`}
//         />
//       </button>
//       <div
//         className={`px-4 py-2 text-gray-600 overflow-hidden transition-all duration-500 ease-in-out ${
//           isOpen ? "bg-custom-yellow-300" : "bg-gray-50"
//         }`}
//         style={{
//           maxHeight: isOpen ? "200px" : "0",
//           opacity: isOpen ? 1 : 0,
//           transitionProperty: "max-height, opacity, background-color",
//           transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
//         }}
//       >
//         <p className="text-sm">{description}</p>
//       </div>
//     </div>
//   );
// };

// export default DropText;

// "use client";
// import React, { useState } from "react";
// import { FaChevronDown } from "react-icons/fa"; // Using Font Awesome chevron, adjust import as needed

// interface DropdownProps {
//   title: string;
//   description: string;
// }

// const DropText: React.FC<DropdownProps> = ({ title, description }) => {
//   const [isOpen, setIsOpen] = useState(false);

//   return (
//     <div
//       className={`w-full max-w-md mx-auto border border-gray-200 rounded-lg shadow-sm overflow-hidden transition-all duration-500 ease-in-out ${
//         isOpen ? "bg-custom-yellow-200" : "bg-white"
//       }`}
//     >
//       <button
//         type="button"
//         onClick={() => setIsOpen(!isOpen)}
//         className="w-full px-4 py-3 text-left text-gray-800 font-medium focus:outline-none flex justify-between items-center"
//       >
//         <span>{title}</span>
//         <FaChevronDown
//           className={`w-5 h-5 transition-transform duration-300 ease-in-out ${
//             isOpen ? "rotate-180" : ""
//           }`}
//         />
//       </button>
//       <div
//         className="px-4 py-2 text-gray-600 overflow-hidden transition-all duration-500 ease-in-out"
//         style={{
//           maxHeight: isOpen ? "200px" : "0",
//           opacity: isOpen ? 1 : 0,
//           transitionProperty: "max-height, opacity",
//           transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
//         }}
//       >
//         <p className="text-sm">{description}</p>
//       </div>
//     </div>
//   );
// };

// export default DropText;

// "use client";
// import React, { useState } from "react";
// import { FaChevronDown } from "react-icons/fa"; // Using Font Awesome chevron, adjust import as needed

// interface DropdownProps {
//   title: string;
//   description: string;
// }

// const DropText: React.FC<DropdownProps> = ({ title, description }) => {
//   const [isOpen, setIsOpen] = useState(false);

//   return (
//     <div
//       className={`w-full max-w-md mx-auto border border-gray-200 rounded-lg shadow-sm overflow-hidden transition-all duration-500 ease-in-out ${
//         isOpen ? "bg-custom-yellow-200" : "bg-white"
//       }`}
//     >
//       <button
//         type="button"
//         onClick={() => setIsOpen(!isOpen)}
//         className="w-full px-4 py-3 text-left text-gray-800 font-medium focus:outline-none flex justify-between items-center"
//       >
//         <span>{title}</span>
//         <FaChevronDown
//           className={`w-5 h-5 transition-transform duration-300 ease-in-out ${
//             isOpen ? "rotate-180" : ""
//           } ml-4`}
//         />
//       </button>
//       <div
//         className="px-4 py-2 text-gray-600 overflow-hidden transition-all duration-500 ease-in-out"
//         style={{
//           maxHeight: isOpen ? "200px" : "0",
//           opacity: isOpen ? 1 : 0,
//           transitionProperty: "max-height, opacity",
//           transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
//         }}
//       >
//         <p className="text-sm">{description}</p>
//       </div>
//     </div>
//   );
// };

// export default DropText;

"use client";
import React, { useState } from "react";
import { FaChevronDown } from "react-icons/fa"; // Using Font Awesome chevron, adjust import as needed

interface DropdownProps {
  title: string;
  description: string;
}

const DropText: React.FC<DropdownProps> = ({ title, description }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      className={`w-full  mx-auto border border-gray-200 rounded-lg shadow-sm overflow-hidden transition-all duration-500 ease-in-out ${
        isOpen ? "bg-custom-yellow-100" : "bg-white"
      }`}
    >
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-4 py-3 text-left text-gray-800 font-medium focus:outline-none flex justify-between items-center"
      >
        <span className="font-lora font-medium text-sm md:text-base lg:text-[18px]">
          {title}
        </span>
        <FaChevronDown
          className={`w-5 h-5 transition-transform duration-300 ease-in-out cursor-pointer ${
            isOpen ? "rotate-180" : ""
          } ml-8`}
        />
      </button>
      <div
        className="px-4 py-2 text-gray-600 overflow-hidden transition-all duration-500 ease-in-out"
        style={{
          maxHeight: isOpen ? "200px" : "0",
          opacity: isOpen ? 1 : 0,
          transitionProperty: "max-height, opacity",
          transitionTimingFunction: "cubic-bezier(0.4, 0, 0.2, 1)",
        }}
      >
        <p className="text-sm font-lora">{description}</p>
      </div>
    </div>
  );
};

export default DropText;

"use client";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useRef } from "react";
import Slider from "react-slick";
const Collections = () => {
  const sliderRef = useRef<Slider>(null);

  const goToPrev = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  const settings = {
    className: "w-full",
    dots: false,
    infinite: true,
    centerMode: false,
    slidesToShow: 3,
    slidesToScroll: 3,
    variableWidth: true,
    arrows: true,
    autoplay: true,
  };

  return (
    <div className="slider-container relative container mx-auto my-8 px-4">
      <h2 className="text-2xl font-bold mb-6 text-center">Our Collections</h2>
      <div className="h-auto">
        <Slider ref={sliderRef} {...settings}>
          {Array.from({ length: 11 }).map((_, index) => (
            <div key={index} className="">
              <div
                style={{ width: 270, height: 370 }}
                className="relative overflow-hidden rounded-md m-2"
              >
                <Image
                  src="/images/bridal.png"
                  alt="collection1"
                  className="object-cover"
                  fill
                  priority={index < 3}
                />
              </div>
            </div>
          ))}
        </Slider>
        <div className="absolute top-1/2 left-0 right-0 flex justify-between transform -translate-y-1/2 px-4 md:px-8 z-10">
          <Button
            variant="outline"
            size="icon"
            className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md"
            onClick={goToPrev}
          >
            <ChevronLeft className="h-5 w-5 text-custom-red-500" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md"
            onClick={goToNext}
          >
            <ChevronRight className="h-5 w-5 text-custom-red-500" />
          </Button>
        </div>
        <div className="flex items-center justify-center">
          <Button
            variant="outline"
            className="bg-custom-red-500 w-auto mx-auto backdrop-blur-sm rounded-sm text-white hover:bg-custom-red-600 border-none hover:text-white"
          >
            View all Collections
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Collections;

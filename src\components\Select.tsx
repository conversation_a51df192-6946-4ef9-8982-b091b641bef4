"use client";

import { useId, useState, forwardRef } from "react";
import { CheckIcon, ChevronDownIcon } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps {
  options: SelectOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  label?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  className?: string;
  required?: boolean;
  name?: string;
  id?: string;
  error?: string | { message?: string };
  helperText?: string;
}

const Select = forwardRef<HTMLButtonElement, SelectProps>(
  (
    {
      options,
      value: controlledValue,
      onValueChange,
      placeholder = "Select option",
      label,
      searchPlaceholder = "Search...",
      emptyMessage = "No options found.",
      disabled = false,
      className,
      required = false,
      name,
      id: providedId,
      error,
      helperText,
    },
    ref
  ) => {
    const generatedId = useId();
    const id = providedId || generatedId;
    const [open, setOpen] = useState<boolean>(false);
    const [internalValue, setInternalValue] = useState<string>("");

    // Use controlled value if provided, otherwise use internal state
    const value =
      controlledValue !== undefined ? controlledValue : internalValue;

    const handleValueChange = (newValue: string) => {
      if (controlledValue === undefined) {
        setInternalValue(newValue);
      }
      onValueChange?.(newValue);
    };

    const selectedOption = options.find((option) => option.value === value);

    // Extract error message from string or FieldError
    const errorMessage = typeof error === "string" ? error : error?.message;

    return (
      <div className={cn("*:not-first:mt-2", className)}>
        {label && (
          <Label
            htmlFor={id}
            className={
              required
                ? "after:content-['*'] after:text-red-500 after:ml-1"
                : ""
            }
          >
            {label}
          </Label>
        )}
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              ref={ref}
              id={id}
              name={name}
              variant="outline"
              role="combobox"
              aria-expanded={open}
              aria-required={required}
              aria-describedby={
                errorMessage
                  ? `${id}-error`
                  : helperText
                  ? `${id}-helper`
                  : undefined
              }
              disabled={disabled}
              className={cn(
                "bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]",
                errorMessage && "border-red-500 focus-visible:ring-red-500"
              )}
            >
              <span
                className={cn("truncate", !value && "text-muted-foreground")}
              >
                {selectedOption?.label || placeholder}
              </span>
              <ChevronDownIcon
                size={16}
                className="text-muted-foreground/80 shrink-0"
                aria-hidden="true"
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0"
            align="start"
          >
            <Command>
              <CommandInput placeholder={searchPlaceholder} />
              <CommandList>
                <CommandEmpty>{emptyMessage}</CommandEmpty>
                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      disabled={option.disabled}
                      onSelect={(currentValue) => {
                        const newValue =
                          currentValue === value ? "" : currentValue;
                        handleValueChange(newValue);
                        setOpen(false);
                      }}
                    >
                      {option.label}
                      {value === option.value && (
                        <CheckIcon size={16} className="ml-auto" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        {errorMessage && (
          <p id={`${id}-error`} className="text-sm text-red-500 mt-1">
            {errorMessage}
          </p>
        )}
        {!errorMessage && helperText && (
          <p id={`${id}-helper`} className="text-sm text-muted-foreground mt-1">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

Select.displayName = "Select";

export default Select;

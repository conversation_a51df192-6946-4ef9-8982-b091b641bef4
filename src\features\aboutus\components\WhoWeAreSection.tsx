// import Image from "next/image";
// import React from "react";

// const WhoWeAreSection = () => {
//   const imageList = ["/aboutpage/topimage1.png", "/aboutpage/topimage2.png"];

//   return (
//     <div className="bg-white container mx-auto p-3 md:p-14 lg:p-16 flex flex-col md:flex-row items-center gap-8">
//       <div className="flex flex-col gap-2">
//         <p className="text-sm md:text-base lg:text-lg font-lora text-custom-violet-500 font-semibold">
//           Who We are
//         </p>

//         <h2 className="text-black text-xl md:text-2xl lg:text-[28px] font-semibold">
//           Crafting Stories, Not Just Jewellery.
//         </h2>
//         <p className=" text-sm md:text-sm lg:text-base text-custom-textgray">
//           At Ridhi Sidhi we're more than a jewellery brand - we're storytellers
//           of sparkle and elegance. Born from a passion for timeless
//           craftsmanship and modern aesthetics, we create pieces that celebrate
//           individuality, love and life's most meaningful moments. Whether it's a
//           delicate diamond ring or a bold gold statement, our designs are
//           crafted to reflect who you are and what you cherish. We believe
//           jewellery isn't just worn - it's lived in, loved, and passed on as a
//           legacy.
//         </p>
//       </div>
//       <div className="flex items-center gap-2">
//         {imageList &&
//           imageList.map((item, index) => (
//             <Image
//               key={index}
//               src={item}
//               alt="Images"
//               height={1000}
//               width={1200}
//             />
//           ))}
//       </div>
//     </div>
//   );
// };

// export default WhoWeAreSection;

// Second

// import Image from "next/image";
// import React from "react";
// import { EllipseLeft, EllipseRight } from "./ellipse";

// const WhoWeAreSection = () => {
//   const imageList = ["/aboutpage/topimage1.png", "/aboutpage/topimage2.png"];

//   return (
//     <div className="relative bg-white container mx-auto p-3 md:p-14 lg:p-16 overflow-hidden">
//       {/* Bottom Left Ellipse */}
//       <div className="absolute bottom-0 left-0 transform translate-y-1/2 -translate-x-1/4">
//         <EllipseLeft />
//       </div>
//       {/* Top Right Ellipse */}
//       <div className="absolute top-0 right-0 transform -translate-y-1/2 translate-x-1/4">
//         <EllipseRight />
//       </div>

//       <div className="flex flex-col md:flex-row items-center gap-8 z-10">
//         <div className="flex flex-col gap-2">
//           <p className="text-sm md:text-base lg:text-lg font-lora text-custom-violet-500 font-semibold">
//             Who We are
//           </p>
//           <h2 className="text-black text-xl md:text-2xl lg:text-[28px] font-semibold">
//             Crafting Stories, Not Just Jewellery.
//           </h2>
//           <p className="text-sm md:text-sm lg:text-base text-custom-textgray">
//             At Ridhi Sidhi we're more than a jewellery brand - we're
//             storytellers of sparkle and elegance. Born from a passion for
//             timeless craftsmanship and modern aesthetics, we create pieces that
//             celebrate individuality, love and life's most meaningful moments.
//             Whether it's a delicate diamond ring or a bold gold statement, our
//             designs are crafted to reflect who you are and what you cherish. We
//             believe jewellery isn't just worn - it's lived in, loved, and passed
//             on as a legacy.
//           </p>
//         </div>
//         <div className="flex items-center gap-2">
//           {imageList &&
//             imageList.map((item, index) => (
//               <Image
//                 key={index}
//                 src={item}
//                 alt="Images"
//                 height={1000}
//                 width={1200}
//                 className="w-auto h-auto max-h-64"
//               />
//             ))}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default WhoWeAreSection;

// Third

import Image from "next/image";
import React from "react";
import { EllipseLeft, EllipseRight } from "./SvgComponent";

const WhoWeAreSection = () => {
  const imageList = ["/aboutpage/topimage1.png", "/aboutpage/topimage2.png"];

  return (
    <div className="relative bg-white container mx-auto p-3 md:p-14 lg:p-16 flex flex-col md:flex-row items-center gap-8 overflow-hidden">
      <div className="absolute bottom-0 left-0 transform translate-y-1/2 -translate-x-1/4">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute top-0 right-0 transform -translate-y-1/2 translate-x-1/4">
        <EllipseRight />
      </div>
      <div className="flex flex-col gap-2">
        <p className="text-sm md:text-base lg:text-lg font-lora text-custom-violet-500 font-semibold">
          Who We are
        </p>

        <h2 className="text-black text-xl md:text-2xl lg:text-[28px] font-semibold">
          Crafting Stories, Not Just Jewellery.
        </h2>
        <p className=" text-sm md:text-sm lg:text-base text-custom-textgray">
          At Ridhi Sidhi we're more than a jewellery brand - we're storytellers
          of sparkle and elegance. Born from a passion for timeless
          craftsmanship and modern aesthetics, we create pieces that celebrate
          individuality, love and life's most meaningful moments. Whether it's a
          delicate diamond ring or a bold gold statement, our designs are
          crafted to reflect who you are and what you cherish. We believe
          jewellery isn't just worn - it's lived in, loved, and passed on as a
          legacy.
        </p>
      </div>
      <div className="flex items-center gap-2">
        {imageList &&
          imageList.map((item, index) => (
            <Image
              key={index}
              src={item}
              alt="Images"
              height={1000}
              width={1200}
            />
          ))}
      </div>
    </div>
  );
};

export default WhoWeAreSection;

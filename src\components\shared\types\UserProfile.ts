// User data interface
export interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  bio: string;
  avatar: string;
  joinDate: string;
  membershipTier: "Bronze" | "Silver" | "Gold" | "Platinum";
  totalOrders: number;
  totalSpent: number;
  wishlistCount: number;
  reviewsCount: number;
  loyaltyPoints: number;
}

// Profile section type
export type ProfileSection =
  | "overview"
  | "personal"
  | "orders"
  | "wishlist"
  | "reviews"
  | "settings"
  | "notifications"
  | "security"
  | "payment";

// Component props interface
export interface UserProfileProps {
  user?: UserData;
  onUpdateProfile?: (data: Partial<UserData>) => void;
  onUploadAvatar?: (file: File) => void;
}

// Order interface for the orders section
export interface Order {
  id: string;
  date: string;
  status: "Processing" | "Shipped" | "Delivered" | "Cancelled";
  total: string;
  items: string;
  statusColor: string;
}

// Wishlist item interface
export interface WishlistItem {
  id: string;
  name: string;
  price: string;
  image: string;
  inStock: boolean;
}

// Review interface
export interface Review {
  id: string;
  product: string;
  rating: number;
  date: string;
  review: string;
}

// Activity item interface
export interface ActivityItem {
  id: string;
  type: "order" | "wishlist" | "review" | "account";
  title: string;
  description: string;
  date: string;
  icon: string;
}

// Sidebar navigation item interface
export interface SidebarItem {
  id: ProfileSection;
  label: string;
  icon: any; // Lucide icon component
}

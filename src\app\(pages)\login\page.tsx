import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import Image from "next/image";
import React from "react";
import { z } from "zod";
import {
  DynamicFormRenderer,
  type FormConfig,
} from "@/components/ui/dynamic-form-renderer";
import { Phone, Lock } from "lucide-react";
import { Button } from "@/components/ui/button";

const page = () => {
  // Login form validation schema
  const loginSchema = z.object({
    phone: z.string().min(10, "Phone number must be at least 10 digits"),
    password: z.string().min(1, "Password is required"),
    rememberMe: z.boolean().optional(),
  });

  // Login form configuration
  const loginFormConfig: FormConfig = {
    title: "Welcome Back !",
    description: "Login to continue your sparkle story.",
    schema: loginSchema,
    defaultValues: {
      phone: "",
      password: "",
      rememberMe: false,
    },
    sections: [
      {
        columns: 1,
        fields: [
          {
            name: "phone",
            type: "tel",
            label: "Phone Number",
            placeholder: "Enter phone number",
            required: true,
            leftSection: <Phone className="h-4 w-4" />,
            layout: "full",
          },
          {
            name: "password",
            type: "password",
            label: "Password",
            placeholder: "Enter password",
            required: true,
            withPasswordToggle: true,
            leftSection: <Lock className="h-4 w-4" />,
            layout: "full",
          },
          {
            name: "rememberMe",
            type: "checkbox",
            checkboxLabel: "Remember Me",
            layout: "full",
          },
        ],
      },
    ],
    onSubmit: async (data) => {
      console.log("Login form submitted:", data);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      alert("Login successful!");
    },
    submitButtonText: "Login",
    showResetButton: false,
  };

  return (
    <div className="relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden flex flex-col md:flex-row items-center gap-8">
      {/* Image Grid */}
      <div className="grid grid-cols-7 grid-rows-5 gap-2 w-full max-w-4xl h-[500px]">
        {/* Top Left - Large image (Bracelet) */}
        <div className="col-start-1 col-end-4 row-start-1 row-end-4">
          <Image
            src="/login/topleft.png"
            alt="Gold bracelet"
            height={300}
            width={240}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Top Right - Small image (Necklace) */}
        <div className="col-start-4 col-end-6 row-start-1 row-end-3">
          <Image
            src="/login/topright.png"
            alt="Gold necklace"
            height={160}
            width={160}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Left - Small image (Pearl Necklace) */}
        <div className="col-start-1 col-end-3 row-start-4 row-end-6">
          <Image
            src="/login/bottomleft.png"
            alt="Pearl necklace"
            height={160}
            width={160}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Right - Large image (Earrings) */}
        <div className="col-start-4 col-end-8 row-start-3 row-end-6">
          <Image
            src="/login/bottomright.png"
            alt="Gold earrings"
            height={300}
            width={320}
            className="object-cover w-full h-full"
          />
        </div>
      </div>

      {/* Login Form */}
      <div className="w-full max-w-md z-10">
        {/* Custom Login Form Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Welcome Back !
          </h1>
          <p className="text-gray-600">Login to continue your sparkle story.</p>
        </div>

        {/* Custom Login Form */}
        <div className="space-y-4">
          {/* Phone Number Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="tel"
                placeholder="Enter phone number"
                className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="password"
                placeholder="Enter password"
                className="w-full pl-10 pr-10 py-3 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
              </button>
            </div>
            {/* Error Message */}
            <div className="flex items-center mt-1 text-red-600 text-sm">
              <svg
                className="h-4 w-4 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              Incorrect Password
            </div>
          </div>

          {/* Remember Me and Forgot Password */}
          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-600">Remember Me</span>
            </label>
            <button className="text-sm text-gray-600 hover:text-gray-800">
              Forgot password ?
            </button>
          </div>

          {/* Login Button */}
          <Button className="w-full bg-red-600 hover:bg-red-700 text-white py-3 rounded-md font-medium">
            Login
          </Button>
        </div>

        {/* Additional Login Options */}
        <div className="mt-6 space-y-4">
          {/* OR Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="bg-white px-2 text-gray-500">OR</span>
            </div>
          </div>

          {/* Google Login Button */}
          <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2 py-3"
          >
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continue with Google
          </Button>

          {/* Sign Up Link */}
          <div className="text-center text-sm">
            <span className="text-gray-600">Don't have an account? </span>
            <button className="text-blue-600 hover:text-blue-800 font-medium">
              Sign Up
            </button>
          </div>
        </div>
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default page;

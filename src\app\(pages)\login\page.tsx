import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import Image from "next/image";
import React from "react";

const page = () => {
  return (
    <div className=" relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden flex flex-col md:flex-row items-center ">
      <div className="grid grid-cols-2 gap-4 max-w-2xl">
        {/* Top Row */}
        <div className="relative">
          <Image
            src="/login/topleft.png"
            alt="Gold bracelet"
            height={300}
            width={400}
            className="rounded-lg object-cover w-full h-64"
          />
        </div>
        <div className="relative">
          <Image
            src="/login/topright.png"
            alt="Gold necklace"
            height={200}
            width={300}
            className="rounded-lg object-cover w-full h-48"
          />
        </div>

        {/* Bottom Row */}
        <div className="relative">
          <Image
            src="/login/bottomleft.png"
            alt="Pearl necklace"
            height={200}
            width={300}
            className="rounded-lg object-cover w-full h-48"
          />
        </div>
        <div className="relative">
          <Image
            src="/login/bottomright.png"
            alt="Gold earrings"
            height={300}
            width={400}
            className="rounded-lg object-cover w-full h-64"
          />
        </div>
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default page;

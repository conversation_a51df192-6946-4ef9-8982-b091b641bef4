import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import Image from "next/image";
import React from "react";

const page = () => {
  return (
    <div className=" relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden flex flex-col md:flex-row items-center ">
      <div className="grid grid-cols-6 grid-rows-6 gap-2 w-full max-w-4xl h-[500px]">
        {/* Top Left - Large image (Bracelet) */}
        <div className="col-start-1 col-end-4 row-start-1 row-end-5">
          <Image
            src="/login/topleft.png"
            alt="Gold bracelet"
            height={400}
            width={300}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Top Right - Small image (Necklace) */}
        <div className="col-start-4 col-end-7 row-start-1 row-end-3">
          <Image
            src="/login/topright.png"
            alt="Gold necklace"
            height={200}
            width={300}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Left - Small image (Pearl Necklace) */}
        <div className="col-start-1 col-end-3 row-start-5 row-end-7">
          <Image
            src="/login/bottomleft.png"
            alt="Pearl necklace"
            height={200}
            width={200}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Right - Large image (Earrings) */}
        <div className="col-start-3 col-end-7 row-start-3 row-end-7">
          <Image
            src="/login/bottomright.png"
            alt="Gold earrings"
            height={400}
            width={400}
            className="object-cover w-full h-full"
          />
        </div>
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default page;

import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import Image from "next/image";
import React from "react";

const page = () => {
  return (
    <div className=" relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden flex flex-col md:flex-row items-center ">
      <div className="grid grid-cols-5 grid-rows-4 gap-2 w-full max-w-4xl h-[500px]">
        {/* Top Left - Large vertical image (Bracelet) - spans 2 columns, 3 rows */}
        <div className="col-span-2 row-span-3">
          <Image
            src="/login/topleft.png"
            alt="Gold bracelet"
            height={375}
            width={240}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Top Right - Medium horizontal image (Necklace) - spans 3 columns, 2 rows */}
        <div className="col-span-3 row-span-2">
          <Image
            src="/login/topright.png"
            alt="Gold necklace"
            height={225}
            width={360}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Left - Small square image (<PERSON> Necklace) - spans 2 columns, 1 row */}
        <div className="col-span-2 row-span-1">
          <Image
            src="/login/bottomleft.png"
            alt="Pearl necklace"
            height={125}
            width={240}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Right - Large square image (Earrings) - spans 3 columns, 2 rows */}
        <div className="col-span-3 row-span-2">
          <Image
            src="/login/bottomright.png"
            alt="Gold earrings"
            height={250}
            width={360}
            className="object-cover w-full h-full"
          />
        </div>
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default page;

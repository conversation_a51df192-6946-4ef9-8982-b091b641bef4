import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import Image from "next/image";
import React from "react";

const page = () => {
  return (
    <div className=" relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden flex flex-col md:flex-row items-center ">
      <div className="relative max-w-4xl w-full h-[500px]">
        {/* Top Left - Large Image (Bracelet) - Takes up most of left side */}
        <div className="absolute top-0 left-0 w-[60%] h-[70%]">
          <Image
            src="/login/topleft.png"
            alt="Gold bracelet"
            height={400}
            width={500}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Top Right - Small Image (Necklace) */}
        <div className="absolute top-0 right-0 w-[38%] h-[35%]">
          <Image
            src="/login/topright.png"
            alt="Gold necklace"
            height={200}
            width={300}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Left - Small Image (<PERSON> Necklace) */}
        <div className="absolute bottom-0 left-0 w-[35%] h-[28%]">
          <Image
            src="/login/bottomleft.png"
            alt="Pearl necklace"
            height={150}
            width={250}
            className="object-cover w-full h-full"
          />
        </div>

        {/* Bottom Right - Large Image (Earrings) */}
        <div className="absolute bottom-0 right-0 w-[63%] h-[63%]">
          <Image
            src="/login/bottomright.png"
            alt="Gold earrings"
            height={350}
            width={450}
            className="object-cover w-full h-full"
          />
        </div>
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default page;

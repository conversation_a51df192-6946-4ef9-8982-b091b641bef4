import {
  EllipseLeft,
  EllipseRight,
} from "@/features/aboutus/components/SvgComponent";
import Image from "next/image";
import React from "react";

const page = () => {
  return (
    <div className=" relative py-4 px-4 container mx-auto md:px-24 lg:px-36 overflow-hidden flex flex-col md:flex-row items-center ">
      <div className="relative max-w-4xl w-full h-[600px]">
        {/* Top Left - Large Image (Bracelet) */}
        <div className="absolute top-0 left-0 w-[45%] h-[55%]">
          <Image
            src="/login/topleft.png"
            alt="Gold bracelet"
            height={400}
            width={500}
            className="rounded-lg object-cover w-full h-full shadow-lg"
          />
        </div>

        {/* Top Right - Medium Image (Necklace) */}
        <div className="absolute top-0 right-0 w-[50%] h-[40%]">
          <Image
            src="/login/topright.png"
            alt="Gold necklace"
            height={300}
            width={400}
            className="rounded-lg object-cover w-full h-full shadow-lg"
          />
        </div>

        {/* Bottom Left - Medium Image (Pearl Necklace) */}
        <div className="absolute bottom-0 left-0 w-[40%] h-[40%]">
          <Image
            src="/login/bottomleft.png"
            alt="Pearl necklace"
            height={300}
            width={350}
            className="rounded-lg object-cover w-full h-full shadow-lg"
          />
        </div>

        {/* Bottom Right - Large Image (Earrings) */}
        <div className="absolute bottom-0 right-0 w-[55%] h-[55%]">
          <Image
            src="/login/bottomright.png"
            alt="Gold earrings"
            height={400}
            width={500}
            className="rounded-lg object-cover w-full h-full shadow-lg"
          />
        </div>
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/leftswirl.png"
          alt="Left swirl image"
          height={500}
          width={500}
        />
      </div>
      {/* Right Swirl as Background */}
      <div className="absolute right-0 top-96 transform -translate-y-1/2 z-0">
        <Image
          src="/goldexchangepage/rightswirl.png"
          alt="Right swirl image"
          height={500}
          width={500}
        />
      </div>

      <div className="absolute left-0 top-96 transform -translate-y-1/2 z-0">
        <EllipseLeft />
      </div>
      {/* Top Right Ellipse */}

      <div className="absolute left-[73rem] top-96 transform -translate-y-1/2 z-0">
        <EllipseRight />
      </div>
    </div>
  );
};

export default page;

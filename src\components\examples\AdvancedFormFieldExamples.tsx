"use client";

import React, { useState } from "react";
import { AdvancedInputField, AdvancedTextareaField } from "@/components/ui/advanced-form-field";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, User, Phone, Search, Lock, Star, MessageSquare } from "lucide-react";

export default function AdvancedFormFieldExamples() {
  const [formData, setFormData] = useState({
    email: "",
    username: "",
    phone: "",
    password: "",
    confirmPassword: "",
    bio: "",
    search: "",
    feedback: "",
    review: "",
  });

  const [validationStates, setValidationStates] = useState<Record<string, any>>({});

  const handleInputChange = (field: string) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = event.target.value;
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Real-time validation examples
    if (field === "email") {
      if (!value) {
        setValidationStates((prev) => ({ ...prev, email: { error: "Email is required" } }));
      } else if (!/\S+@\S+\.\S+/.test(value)) {
        setValidationStates((prev) => ({ ...prev, email: { warning: "Please enter a valid email" } }));
      } else {
        setValidationStates((prev) => ({ ...prev, email: { success: "Email looks good!" } }));
      }
    }

    if (field === "username") {
      if (!value) {
        setValidationStates((prev) => ({ ...prev, username: {} }));
      } else if (value.length < 3) {
        setValidationStates((prev) => ({ ...prev, username: { warning: "Username should be at least 3 characters" } }));
      } else if (value.length >= 3 && value.length <= 20) {
        setValidationStates((prev) => ({ ...prev, username: { success: "Username available!" } }));
      } else {
        setValidationStates((prev) => ({ ...prev, username: { error: "Username too long" } }));
      }
    }

    if (field === "password") {
      if (!value) {
        setValidationStates((prev) => ({ ...prev, password: {} }));
      } else if (value.length < 8) {
        setValidationStates((prev) => ({ ...prev, password: { error: "Password must be at least 8 characters" } }));
      } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
        setValidationStates((prev) => ({ ...prev, password: { warning: "Include uppercase, lowercase, and numbers" } }));
      } else {
        setValidationStates((prev) => ({ ...prev, password: { success: "Strong password!" } }));
      }
    }
  };

  const handleClear = (field: string) => () => {
    setFormData((prev) => ({
      ...prev,
      [field]: "",
    }));
    setValidationStates((prev) => ({ ...prev, [field]: {} }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-custom-red-500 mb-2">
          Advanced Mantine-like Form Fields
        </h1>
        <p className="text-muted-foreground">
          Enhanced form components with validation states, character counting, and advanced features
        </p>
      </div>

      {/* Real-time Validation */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Real-time Validation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <AdvancedInputField
              label="Email Address"
              type="email"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleInputChange("email")}
              {...validationStates.email}
              required
              clearable
              onClear={handleClear("email")}
              leftSection={<Mail className="h-4 w-4" />}
              description="We'll send you updates about your orders"
            />

            <AdvancedInputField
              label="Username"
              placeholder="Choose a username"
              value={formData.username}
              onChange={handleInputChange("username")}
              {...validationStates.username}
              withAsterisk
              clearable
              onClear={handleClear("username")}
              leftSection={<User className="h-4 w-4" />}
              withCharacterCount
              maxLength={20}
              description="3-20 characters, letters and numbers only"
            />
          </div>

          <AdvancedInputField
            label="Password"
            type="password"
            placeholder="Create a strong password"
            value={formData.password}
            onChange={handleInputChange("password")}
            {...validationStates.password}
            required
            withPasswordToggle
            leftSection={<Lock className="h-4 w-4" />}
            description="Must contain uppercase, lowercase, and numbers"
          />
        </CardContent>
      </Card>

      {/* Character Counting & Auto-resize */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Character Counting & Auto-resize</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <AdvancedTextareaField
            label="Bio"
            placeholder="Tell us about yourself..."
            value={formData.bio}
            onChange={handleInputChange("bio")}
            withCharacterCount
            maxLength={500}
            clearable
            onClear={handleClear("bio")}
            autoResize
            description="This textarea auto-resizes as you type"
            warning={formData.bio.length > 400 ? "Approaching character limit" : undefined}
            error={formData.bio.length > 500 ? "Character limit exceeded" : undefined}
          />

          <div className="grid md:grid-cols-2 gap-6">
            <AdvancedTextareaField
              label="Product Review"
              placeholder="Share your experience..."
              value={formData.review}
              onChange={handleInputChange("review")}
              withCharacterCount
              maxLength={1000}
              rows={4}
              resize="vertical"
              description="Help other customers with your honest review"
            />

            <AdvancedTextareaField
              label="Feedback"
              placeholder="Any suggestions or feedback?"
              value={formData.feedback}
              onChange={handleInputChange("feedback")}
              rows={4}
              resize="none"
              clearable
              onClear={handleClear("feedback")}
              description="We value your feedback to improve our service"
            />
          </div>
        </CardContent>
      </Card>

      {/* Validation States */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Validation States</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <AdvancedInputField
              label="Valid Input"
              placeholder="This field is valid"
              value="<EMAIL>"
              validationState="valid"
              success="Email format is correct"
              leftSection={<Mail className="h-4 w-4" />}
            />

            <AdvancedInputField
              label="Invalid Input"
              placeholder="This field has an error"
              value="invalid-email"
              validationState="invalid"
              error="Please enter a valid email address"
              leftSection={<Mail className="h-4 w-4" />}
            />

            <AdvancedInputField
              label="Warning Input"
              placeholder="This field has a warning"
              value="test@"
              validationState="warning"
              warning="Email appears incomplete"
              leftSection={<Mail className="h-4 w-4" />}
            />

            <AdvancedInputField
              label="Default Input"
              placeholder="Normal input field"
              leftSection={<User className="h-4 w-4" />}
              description="This is a normal input field"
            />
          </div>
        </CardContent>
      </Card>

      {/* Size Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Size Variants</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <AdvancedInputField
              label="Small Input"
              placeholder="Small size input"
              size="sm"
              leftSection={<Search className="h-3 w-3" />}
              clearable
              onClear={() => {}}
            />

            <AdvancedInputField
              label="Medium Input (Default)"
              placeholder="Medium size input"
              size="md"
              leftSection={<Search className="h-4 w-4" />}
              clearable
              onClear={() => {}}
            />

            <AdvancedInputField
              label="Large Input"
              placeholder="Large size input"
              size="lg"
              leftSection={<Search className="h-5 w-5" />}
              clearable
              onClear={() => {}}
            />
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <AdvancedTextareaField
              label="Small Textarea"
              placeholder="Small size..."
              size="sm"
              rows={3}
            />

            <AdvancedTextareaField
              label="Medium Textarea"
              placeholder="Medium size..."
              size="md"
              rows={3}
            />

            <AdvancedTextareaField
              label="Large Textarea"
              placeholder="Large size..."
              size="lg"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Advanced Features */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Advanced Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <AdvancedInputField
              label="Search with Action"
              placeholder="Search products..."
              value={formData.search}
              onChange={handleInputChange("search")}
              leftSection={<Search className="h-4 w-4" />}
              rightSection={
                <Button size="sm" variant="ghost" className="h-6 px-2 text-xs">
                  Search
                </Button>
              }
              clearable
              onClear={handleClear("search")}
            />

            <AdvancedInputField
              label="Phone Number"
              type="tel"
              placeholder="+977 98XXXXXXXX"
              value={formData.phone}
              onChange={handleInputChange("phone")}
              leftSection={<Phone className="h-4 w-4" />}
              rightSection={
                <Button size="sm" variant="outline" className="h-6 px-2 text-xs">
                  Verify
                </Button>
              }
              description="We'll send a verification code"
            />
          </div>

          <AdvancedTextareaField
            label="Product Review with Rating"
            placeholder="Write your detailed review..."
            rows={4}
            withCharacterCount
            maxLength={2000}
            clearable
            onClear={() => {}}
            description="Share your experience to help other customers"
            success="Thank you for your detailed review!"
          />
        </CardContent>
      </Card>

      {/* Disabled State */}
      <Card>
        <CardHeader>
          <CardTitle className="text-custom-blue-500">Disabled State</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <AdvancedInputField
              label="Disabled Input"
              placeholder="This field is disabled"
              value="Cannot edit this"
              disabled
              leftSection={<User className="h-4 w-4" />}
              description="This field cannot be edited"
            />

            <AdvancedTextareaField
              label="Disabled Textarea"
              placeholder="This textarea is disabled"
              value="This content cannot be modified"
              disabled
              rows={3}
              description="This field is read-only"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

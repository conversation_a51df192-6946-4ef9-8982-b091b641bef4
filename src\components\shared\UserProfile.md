# UserProfile Component

A comprehensive, dynamic user profile component for managing user information, orders, wishlist, reviews, and account settings. Features a sidebar navigation with multiple sections and full editing capabilities.

## Features

- **Multi-Section Interface**: Overview, Personal Info, Orders, Wishlist, Reviews, Settings
- **Editable Profile**: In-place editing with save/cancel functionality
- **Avatar Upload**: Profile picture upload with preview
- **Membership Tiers**: Visual membership tier badges (Bronze, Silver, Gold, Platinum)
- **Statistics Dashboard**: Order count, total spent, wishlist items, loyalty points
- **Recent Activity**: Timeline of user actions
- **Responsive Design**: Mobile-first responsive layout
- **Type Safe**: Full TypeScript support

## Usage

### Basic Usage

```tsx
import UserProfile from "@/components/shared/UserProfile";

function ProfilePage() {
  return <UserProfile />;
}
```

### Custom User Data

```tsx
import UserProfile from "@/components/shared/UserProfile";

const customUser = {
  id: "1",
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  dateOfBirth: "1990-01-01",
  gender: "Male",
  address: {
    street: "123 Main St",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "United States",
  },
  bio: "Jewelry enthusiast...",
  avatar: "/path/to/avatar.jpg",
  joinDate: "2022-01-01",
  membershipTier: "Gold",
  totalOrders: 15,
  totalSpent: 5000.00,
  wishlistCount: 10,
  reviewsCount: 8,
  loyaltyPoints: 1200,
};

function ProfilePage() {
  return <UserProfile user={customUser} />;
}
```

### With Event Handlers

```tsx
import UserProfile from "@/components/shared/UserProfile";

function ProfilePage() {
  const handleUpdateProfile = (data) => {
    // Handle profile update
    console.log("Updating profile:", data);
    // Make API call to update user profile
  };

  const handleAvatarUpload = (file) => {
    // Handle avatar upload
    console.log("Uploading avatar:", file);
    // Upload file to storage service
  };

  return (
    <UserProfile
      user={userData}
      onUpdateProfile={handleUpdateProfile}
      onUploadAvatar={handleAvatarUpload}
    />
  );
}
```

## Props

### UserProfileProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `user` | `UserData` | `defaultUser` | User data object |
| `onUpdateProfile` | `(data: Partial<UserData>) => void` | - | Profile update handler |
| `onUploadAvatar` | `(file: File) => void` | - | Avatar upload handler |

### UserData Interface

```tsx
interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  bio: string;
  avatar: string;
  joinDate: string;
  membershipTier: "Bronze" | "Silver" | "Gold" | "Platinum";
  totalOrders: number;
  totalSpent: number;
  wishlistCount: number;
  reviewsCount: number;
  loyaltyPoints: number;
}
```

## Sections

### Overview
- Statistics cards (orders, spending, wishlist, loyalty points)
- Recent activity timeline
- Membership tier display

### Personal Information
- Editable personal details
- Address management
- Bio/description field
- In-place editing with save/cancel

### My Orders
- Order history with status badges
- Order details and tracking
- Action buttons for each order

### Wishlist
- Grid layout of wishlist items
- Add to cart functionality
- Remove from wishlist option

### Reviews
- User's product reviews
- Star ratings display
- Review dates and content

### Settings
- Account configuration options
- Privacy settings
- Account deletion option

## Styling

The component uses the project's design system:

- **Colors**: `custom-blue-500`, `custom-red-500` for primary actions
- **Typography**: Lora font family
- **Components**: Card, Button, Badge, Avatar from UI library
- **Layout**: Responsive grid with sidebar navigation

## Customization

### Membership Tier Colors

```tsx
const getTierColor = (tier: string) => {
  switch (tier) {
    case "Bronze": return "bg-amber-100 text-amber-800";
    case "Silver": return "bg-gray-100 text-gray-800";
    case "Gold": return "bg-yellow-100 text-yellow-800";
    case "Platinum": return "bg-purple-100 text-purple-800";
    default: return "bg-gray-100 text-gray-800";
  }
};
```

### Adding New Sections

To add a new section to the profile:

1. Add the section to the `ProfileSection` type
2. Add the sidebar item to `sidebarItems` array
3. Add the section content in the main content area

## Integration

The component integrates seamlessly with:

- Form validation libraries (React Hook Form, Formik)
- File upload services
- User management APIs
- Authentication systems
- State management (Redux, Zustand)

## Examples

See `src/components/examples/UserProfileExample.tsx` for comprehensive usage examples.

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- Focus management
- Color contrast compliance

"use client";

import React from "react";
import { z } from "zod";
import {
  DynamicFormRenderer,
  type FormConfig,
} from "@/components/ui/dynamic-form-renderer";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Star,
  MessageSquare,
  Lock,
} from "lucide-react";

export default function DynamicFormExample() {
  // Custom validation schema
  const userRegistrationSchema = z
    .object({
      // Personal Information
      firstName: z.string().min(2, "First name must be at least 2 characters"),
      lastName: z.string().min(2, "Last name must be at least 2 characters"),
      email: z.string().email("Please enter a valid email address"),
      phone: z.string().min(10, "Phone number must be at least 10 digits"),

      // Account Information
      password: z.string().min(8, "Password must be at least 8 characters"),
      confirmPassword: z.string(),

      // Preferences
      gender: z.string().min(1, "Please select your gender"),
      interests: z
        .array(z.string())
        .min(1, "Please select at least one interest"),
      newsletter: z.boolean(),

      // Address
      country: z.string().min(1, "Please select your country"),
      city: z.string().min(2, "City name must be at least 2 characters"),

      // Additional
      bio: z
        .string()
        .max(500, "Bio must be less than 500 characters")
        .optional(),
      rating: z.string().min(1, "Please provide a rating"),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    });

  // Form configuration
  const formConfig: FormConfig = {
    title: "User Registration Form",
    description:
      "Complete this form to create your account and set up your profile",
    schema: userRegistrationSchema,
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
      gender: "",
      interests: [],
      newsletter: false,
      country: "",
      city: "",
      bio: "",
      rating: "",
    },
    sections: [
      {
        title: "Personal Information",
        description: "Tell us about yourself",
        columns: 2,
        fields: [
          {
            name: "firstName",
            type: "text",
            label: "First Name",
            placeholder: "Enter your first name",
            required: true,
            leftSection: <User className="h-4 w-4" />,
            layout: "half",
          },
          {
            name: "lastName",
            type: "text",
            label: "Last Name",
            placeholder: "Enter your last name",
            required: true,
            leftSection: <User className="h-4 w-4" />,
            layout: "half",
          },
          {
            name: "email",
            type: "email",
            label: "Email Address",
            placeholder: "Enter your email",
            required: true,
            leftSection: <Mail className="h-4 w-4" />,
            description: "We'll use this for account notifications",
            layout: "half",
          },
          {
            name: "phone",
            type: "tel",
            label: "Phone Number",
            placeholder: "+977 98XXXXXXXX",
            required: true,
            leftSection: <Phone className="h-4 w-4" />,
            layout: "half",
          },
        ],
      },
      {
        title: "Account Security",
        description: "Set up your login credentials",
        columns: 2,
        fields: [
          {
            name: "password",
            type: "password",
            label: "Password",
            placeholder: "Create a strong password",
            required: true,
            withPasswordToggle: true,
            leftSection: <Lock className="h-4 w-4" />,
            description: "Must be at least 8 characters long",
            layout: "half",
          },
          {
            name: "confirmPassword",
            type: "password",
            label: "Confirm Password",
            placeholder: "Confirm your password",
            required: true,
            withPasswordToggle: true,
            leftSection: <Lock className="h-4 w-4" />,
            layout: "half",
          },
        ],
      },
      {
        title: "Preferences & Interests",
        description: "Help us personalize your experience",
        columns: 1,
        fields: [
          {
            name: "gender",
            type: "radio",
            label: "Gender",
            required: true,
            orientation: "horizontal",
            options: [
              { value: "male", label: "Male" },
              { value: "female", label: "Female" },
              { value: "other", label: "Other" },
              { value: "prefer-not-to-say", label: "Prefer not to say" },
            ],
          },
          {
            name: "interests",
            type: "checkbox-group",
            label: "Interests",
            description: "Select all that apply",
            required: true,
            orientation: "horizontal",
            options: [
              {
                value: "jewelry",
                label: "Jewelry",
                description: "Gold, silver, diamonds",
              },
              {
                value: "fashion",
                label: "Fashion",
                description: "Clothing and accessories",
              },
              {
                value: "technology",
                label: "Technology",
                description: "Gadgets and electronics",
              },
              {
                value: "travel",
                label: "Travel",
                description: "Destinations and experiences",
              },
              {
                value: "food",
                label: "Food & Dining",
                description: "Restaurants and cuisine",
              },
              {
                value: "sports",
                label: "Sports",
                description: "Athletic activities",
              },
            ],
          },
          {
            name: "newsletter",
            type: "checkbox",
            checkboxLabel:
              "Subscribe to our newsletter for updates and special offers",
            description: "You can unsubscribe at any time",
          },
        ],
      },
      {
        title: "Location Information",
        description: "Help us provide location-specific services",
        columns: 2,
        fields: [
          {
            name: "country",
            type: "select",
            label: "Country",
            placeholder: "Select your country",
            required: true,
            // leftSection: <MapPin className="h-4 w-4" />,
            layout: "half",
            options: [
              { value: "nepal", label: "Nepal" },
              { value: "india", label: "India" },
              { value: "usa", label: "United States" },
              { value: "uk", label: "United Kingdom" },
              { value: "canada", label: "Canada" },
              { value: "australia", label: "Australia" },
            ],
          },
          {
            name: "city",
            type: "text",
            label: "City",
            placeholder: "Enter your city",
            required: true,
            leftSection: <MapPin className="h-4 w-4" />,
            layout: "half",
          },
        ],
      },
      {
        title: "Additional Information",
        description: "Optional details to complete your profile",
        columns: 1,
        fields: [
          {
            name: "bio",
            type: "textarea",
            label: "Bio",
            placeholder: "Tell us a bit about yourself...",
            rows: 4,
            maxLength: 500,
            description:
              "Share your interests, hobbies, or anything you'd like others to know",
          },
          {
            name: "rating",
            type: "select",
            label: "How did you hear about us?",
            placeholder: "Select an option",
            required: true,
            // leftSection: <Star className="h-4 w-4" />,
            options: [
              { value: "search", label: "Search Engine (Google, Bing, etc.)" },
              { value: "social", label: "Social Media" },
              { value: "friend", label: "Friend or Family" },
              { value: "advertisement", label: "Advertisement" },
              { value: "blog", label: "Blog or Article" },
              { value: "other", label: "Other" },
            ],
          },
        ],
      },
    ],
    onSubmit: async (data) => {
      console.log("Form submitted with data:", data);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      alert("Registration successful! Welcome to our platform.");
    },
    onReset: () => {
      console.log("Form reset");
    },
    submitButtonText: "Create Account",
    resetButtonText: "Clear Form",
    showResetButton: true,
  };

  // Simple contact form example
  const contactFormConfig: FormConfig = {
    title: "Contact Us",
    description: "Get in touch with our team",
    sections: [
      {
        columns: 2,
        fields: [
          {
            name: "name",
            type: "text",
            label: "Full Name",
            placeholder: "Enter your name",
            required: true,
            leftSection: <User className="h-4 w-4" />,
            layout: "half",
          },
          {
            name: "email",
            type: "email",
            label: "Email",
            placeholder: "Enter your email",
            required: true,
            leftSection: <Mail className="h-4 w-4" />,
            layout: "half",
          },
          {
            name: "subject",
            type: "select",
            label: "Subject",
            placeholder: "Select a subject",
            required: true,
            layout: "full",
            options: [
              { value: "general", label: "General Inquiry" },
              { value: "support", label: "Technical Support" },
              { value: "billing", label: "Billing Question" },
              { value: "feedback", label: "Feedback" },
            ],
          },
          {
            name: "message",
            type: "textarea",
            label: "Message",
            placeholder: "Enter your message...",
            required: true,
            rows: 5,
            layout: "full",
            maxLength: 1000,
          },
          {
            name: "urgent",
            type: "checkbox",
            checkboxLabel: "This is urgent and requires immediate attention",
            layout: "full",
          },
        ],
      },
    ],
    onSubmit: async (data) => {
      console.log("Contact form submitted:", data);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      alert("Message sent successfully!");
    },
    submitButtonText: "Send Message",
    showResetButton: false,
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-12">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-custom-red-500 mb-2">
          Dynamic Form Renderer Examples
        </h1>
        <p className="text-muted-foreground">
          Comprehensive form generation with React Hook Form, Zod validation,
          and all field types
        </p>
      </div>

      {/* Complex Registration Form */}
      <div>
        <h2 className="text-2xl font-semibold text-custom-blue-500 mb-6">
          Complex Registration Form
        </h2>
        <DynamicFormRenderer {...formConfig} />
      </div>

      {/* Simple Contact Form */}
      <div>
        <h2 className="text-2xl font-semibold text-custom-blue-500 mb-6">
          Simple Contact Form
        </h2>
        <DynamicFormRenderer {...contactFormConfig} />
      </div>
    </div>
  );
}

# Mantine-like Form Field Components

This documentation covers the comprehensive form field components that provide Mantine-like functionality with labels, validation, error handling, and advanced features.

## Components Overview

### Basic Components

- `InputField` - Basic input field with label, description, and error handling
- `TextareaField` - Basic textarea field with label, description, and error handling

### Advanced Components

- `AdvancedInputField` - Enhanced input with validation states, character counting, and more
- `AdvancedTextareaField` - Enhanced textarea with auto-resize, character counting, and more

## Basic Usage

### InputField

```tsx
import { InputField } from "@/components/ui/form-field";

<InputField
  label="Email Address"
  type="email"
  placeholder="Enter your email"
  value={email}
  onChange={(e) => setEmail(e.target.value)}
  error={emailError}
  required
  description="We'll never share your email"
/>;
```

### TextareaField

```tsx
import { TextareaField } from "@/components/ui/form-field";

<TextareaField
  label="Message"
  placeholder="Enter your message"
  value={message}
  onChange={(e) => setMessage(e.target.value)}
  error={messageError}
  rows={4}
  resize="vertical"
/>;
```

## Advanced Usage

### AdvancedInputField

```tsx
import { AdvancedInputField } from "@/components/ui/advanced-form-field";

<AdvancedInputField
  label="Username"
  value={username}
  onChange={(e) => setUsername(e.target.value)}
  validationState="valid"
  success="Username available!"
  withCharacterCount
  maxLength={20}
  clearable
  onClear={() => setUsername("")}
  leftSection={<User className="h-4 w-4" />}
/>;
```

### AdvancedTextareaField

```tsx
import { AdvancedTextareaField } from "@/components/ui/advanced-form-field";

<AdvancedTextareaField
  label="Bio"
  value={bio}
  onChange={(e) => setBio(e.target.value)}
  withCharacterCount
  maxLength={500}
  autoResize
  clearable
  onClear={() => setBio("")}
  warning={bio.length > 400 ? "Approaching limit" : undefined}
/>;
```

## Props Reference

### Common Props (All Components)

| Prop           | Type                   | Default | Description              |
| -------------- | ---------------------- | ------- | ------------------------ |
| `label`        | `string`               | -       | Field label text         |
| `description`  | `string`               | -       | Help text below label    |
| `error`        | `string`               | -       | Error message            |
| `required`     | `boolean`              | `false` | Shows required indicator |
| `withAsterisk` | `boolean`              | `false` | Alternative to required  |
| `disabled`     | `boolean`              | `false` | Disables the field       |
| `size`         | `"sm" \| "md" \| "lg"` | `"md"`  | Field size               |
| `className`    | `string`               | -       | Container class          |

### InputField Specific Props

| Prop                 | Type                     | Default  | Description           |
| -------------------- | ------------------------ | -------- | --------------------- |
| `type`               | `HTMLInputTypeAttribute` | `"text"` | Input type            |
| `leftSection`        | `ReactNode`              | -        | Icon/content on left  |
| `rightSection`       | `ReactNode`              | -        | Icon/content on right |
| `withPasswordToggle` | `boolean`                | `false`  | Show/hide password    |
| `placeholder`        | `string`                 | -        | Placeholder text      |

### TextareaField Specific Props

| Prop          | Type                                             | Default      | Description      |
| ------------- | ------------------------------------------------ | ------------ | ---------------- |
| `rows`        | `number`                                         | -            | Number of rows   |
| `resize`      | `"none" \| "both" \| "horizontal" \| "vertical"` | `"vertical"` | Resize behavior  |
| `placeholder` | `string`                                         | -            | Placeholder text |

### Advanced Components Additional Props

| Prop                 | Type                                             | Default     | Description            |
| -------------------- | ------------------------------------------------ | ----------- | ---------------------- |
| `validationState`    | `"default" \| "valid" \| "invalid" \| "warning"` | `"default"` | Validation state       |
| `warning`            | `string`                                         | -           | Warning message        |
| `success`            | `string`                                         | -           | Success message        |
| `withCharacterCount` | `boolean`                                        | `false`     | Show character counter |
| `maxLength`          | `number`                                         | -           | Maximum characters     |
| `clearable`          | `boolean`                                        | `false`     | Show clear button      |
| `onClear`            | `() => void`                                     | -           | Clear button handler   |
| `autoResize`         | `boolean`                                        | `false`     | Auto-resize textarea   |

## Features

### 1. Validation States

- **Default**: Normal state
- **Valid**: Green styling with success message
- **Invalid**: Red styling with error message
- **Warning**: Yellow styling with warning message

### 2. Character Counting

```tsx
<AdvancedInputField
  withCharacterCount
  maxLength={100}
  value={value}
  // Shows "25/100" in top-right
/>
```

### 3. Password Toggle

```tsx
<AdvancedInputField
  type="password"
  withPasswordToggle
  // Shows eye icon to toggle visibility
/>
```

### 4. Clear Button

```tsx
<AdvancedInputField
  clearable
  onClear={() => setValue("")}
  // Shows X button when field has value
/>
```

### 5. Auto-resize Textarea

```tsx
<AdvancedTextareaField
  autoResize
  // Grows/shrinks based on content
/>
```

### 6. Left/Right Sections

```tsx
<AdvancedInputField
  leftSection={<Mail className="h-4 w-4" />}
  rightSection={<Button size="sm">Send</Button>}
/>
```

## Styling

### Size Variants

- **Small (`sm`)**: Compact for dense layouts
- **Medium (`md`)**: Default size for most use cases
- **Large (`lg`)**: Prominent for important fields

### Custom Styling

All components accept className props for customization:

- `className` - Container styling
- `labelClassName` - Label styling
- `inputClassName` / `textareaClassName` - Input styling
- `errorClassName` - Error message styling

### Theme Integration

Components use your custom color variables:

- `custom-red-500` for errors and required indicators
- `custom-blue-500` for focus states
- Standard Tailwind colors for other states

## Accessibility

### Built-in Features

- Proper ARIA labels and descriptions
- Screen reader announcements for errors
- Keyboard navigation support
- Focus management
- Required field indicators

### ARIA Attributes

- `aria-invalid` for validation states
- `aria-describedby` for descriptions/errors
- `aria-required` for required fields
- `role="alert"` for error messages

## Examples

See the example components for comprehensive usage:

- `FormFieldExamples.tsx` - Basic form field examples
- `AdvancedFormFieldExamples.tsx` - Advanced features demo

## Integration with Form Libraries

### React Hook Form

```tsx
import { useForm, Controller } from "react-hook-form";

const { control } = useForm();

<Controller
  name="email"
  control={control}
  render={({ field, fieldState }) => (
    <AdvancedInputField
      {...field}
      label="Email"
      error={fieldState.error?.message}
      validationState={fieldState.error ? "invalid" : "default"}
    />
  )}
/>;
```

### Formik

```tsx
import { useFormik } from "formik";

const formik = useFormik({...});

<AdvancedInputField
  label="Email"
  value={formik.values.email}
  onChange={formik.handleChange}
  onBlur={formik.handleBlur}
  error={formik.touched.email && formik.errors.email}
  name="email"
/>
```

## Best Practices

1. **Use appropriate sizes** - `sm` for filters, `md` for forms, `lg` for prominent fields
2. **Provide helpful descriptions** - Guide users with clear instructions
3. **Real-time validation** - Show feedback as users type
4. **Character limits** - Use `withCharacterCount` for limited fields
5. **Clear error messages** - Be specific about what's wrong
6. **Consistent styling** - Use the same size/style within forms

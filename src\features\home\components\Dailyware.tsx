import Image from "next/image";
import { Card } from "@/components/ui/card";

export default function Dailyware() {
  const categories = [
    {
      title: "EVERYDAY CHAINS",
      image: "/placeholder.svg?height=150&width=150",
    },
    {
      title: "ELEGANT STACK RING",
      image: "/placeholder.svg?height=150&width=150",
    },
    {
      title: "DAY-TO-DAY BRACELETS",
      image: "/placeholder.svg?height=150&width=150",
    },
    {
      title: "SOLITAIRE EARRINGS",
      image: "/placeholder.svg?height=150&width=150",
    },
    {
      title: "STYLE PICKS UNDER BUDGETS",
      image: "/placeholder.svg?height=150&width=150",
    },
  ];

  return (
    <section className="container mx-auto md:px-4 px-2">
      <div className="flex flex-col lg:flex-row items-center ga-x-6 gap-y-2 lg:gap-8 bg-custom-violet-50 py-4 md:px-4 px-2 rounded-md">
        {/* Logo Section */}
        <div className="flex-shrink-0 flex flex-col items-center text-center lg:text-left">
          <Image
            width={200}
            height={200}
            src="/images/shop.png"
            alt="logo"
            className="mx-auto"
          />
          <h2 className="text-lg md:text-xl text-center font-bold text-gray-800 max-w-[200px]">
            Simple Luxury for Daily Wear
          </h2>
        </div>

        {/* Categories Grid */}
        <div className="flex-1 w-full">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-y-4 gap-0">
            {categories.map((category, index) => (
              <Card
                key={index}
                className="group cursor-pointer border-0 shadow-none drop-shadow-none bg-transparent transition-all duration-300"
              >
                <div className="p-3 md:p-4">
                  <div className="aspect-square mb-3 bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={"/images/ring.png"}
                      alt={category.title}
                      width={70}
                      height={70}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <h3 className="text-xs md:text-sm font-semibold text-gray-700 text-center leading-tight">
                    {category.title}
                  </h3>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

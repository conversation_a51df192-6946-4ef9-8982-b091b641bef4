// import React from "react";

// interface StepCardProps {
//   stepText: string;
//   description: string;
//   imageSrc: string;
// }

// const StepCard: React.FC<StepCardProps> = ({
//   stepText,
//   description,
//   imageSrc,
// }) => {
//   return (
//     <div className="relative bg-white rounded-lg shadow-md overflow-hidden">
//       <img
//         src={imageSrc}
//         alt="Step illustration"
//         className="w-full h-64 object-cover"
//       />
//       <div className="absolute bottom-20 left-0 bg-custom-red-500 text-white px-8 py-2 font-bold rounded-tr-xl font-lora text-xs md:text-sm lg:text-base">
//         {stepText}
//       </div>
//   <div className="p-4 bg-gray-100 text-gray-700  font-lora text-xs md:text-sm lg:text-base">
//         {description}
//       </div>
//     </div>
//   );
// };

// export default StepCard;

import React from "react";

interface StepCardProps {
  stepText: string;
  description: string;
  imageSrc: string;
}

const StepCard: React.FC<StepCardProps> = ({
  stepText,
  description,
  imageSrc,
}) => {
  return (
    <div className=" bg-white rounded-lg shadow-md overflow-hidden">
      <div className="relative">
        <img
          src={imageSrc}
          alt="Step illustration"
          className="w-full h-64 object-cover"
        />
        <div className="absolute bottom-0 left-0 bg-custom-red-500 rounded-tr-xl text-white px-8 py-2  text-left ont-lora text-xs md:text-sm lg:text-base">
          {stepText}
        </div>
      </div>

      <div className="p-4  text-gray-700  font-lora text-xs md:text-sm lg:text-base">
        {description}
      </div>
    </div>
  );
};

export default StepCard;

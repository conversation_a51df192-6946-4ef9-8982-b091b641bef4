{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@iconify/react": "^6.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "apexcharts": "^5.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.3.5", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}
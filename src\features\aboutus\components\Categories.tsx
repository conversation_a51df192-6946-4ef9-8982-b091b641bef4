// "use client";
// import { Button } from "@/components/ui/button";
// import { ChevronLeft, ChevronRight } from "lucide-react";
// import Image from "next/image";
// import { useRef } from "react";
// import Slider from "react-slick";
// const Categories = () => {
//   const sliderRef = useRef<Slider>(null);

//   const goToPrev = () => {
//     if (sliderRef.current) {
//       sliderRef.current.slickPrev();
//     }
//   };

//   const goToNext = () => {
//     if (sliderRef.current) {
//       sliderRef.current.slickNext();
//     }
//   };

//   const settings = {
//     className: "w-full",
//     dots: false,
//     infinite: true,
//     centerMode: false,
//     slidesToShow: 3,
//     slidesToScroll: 3,
//     variableWidth: true,
//     arrows: true,
//     autoplay: true,
//   };

//   return (
//     <div className="slider-container relative container mx-auto my-8 px-4">
//       <h2 className="text-xl md:text-2xl lg:text-[28px] font-bold mb-6 text-center font-lora">
//         Our Categories
//       </h2>
//       <div className="h-auto">
//         <Slider ref={sliderRef} {...settings}>
//           {Array.from({ length: 11 }).map((_, index) => (
//             <div key={index} className="">
//               <div
//                 style={{ width: 270, height: 270 }}
//                 className="relative overflow-hidden rounded-md m-2"
//               >
//                 <Image
//                   src="/images/bridal.png"
//                   alt="collection1"
//                   className="object-cover"
//                   fill
//                   priority={index < 3}
//                 />
//               </div>
//             </div>
//           ))}
//         </Slider>
//         <div className="absolute top-1/2 left-0 right-0 flex justify-between transform -translate-y-1/2 px-4 md:px-8 z-10">
//           <Button
//             variant="outline"
//             size="icon"
//             className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md"
//             onClick={goToPrev}
//           >
//             <ChevronLeft className="h-5 w-5 text-custom-red-500" />
//           </Button>

//           <Button
//             variant="outline"
//             size="icon"
//             className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md"
//             onClick={goToNext}
//           >
//             <ChevronRight className="h-5 w-5 text-custom-red-500" />
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Categories;

// Second

"use client";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useRef } from "react";
import Slider from "react-slick";

interface Category {
  src: string;
  alt: string;
  text: string;
}

interface CategoriesProps {
  categories: Category[];
}

const Categories = ({ categories }: CategoriesProps) => {
  const sliderRef = useRef<Slider>(null);

  const goToPrev = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  const settings = {
    className: "w-full",
    dots: false,
    infinite: true,
    centerMode: false,
    slidesToShow: 3,
    slidesToScroll: 1,
    variableWidth: true,
    arrows: false,
    autoplay: true,
    autoplaySpeed: 3000,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
    ],
  };

  return (
    <div className="slider-container relative container mx-auto my-6 sm:my-8 px-4 sm:px-6 md:px-8">
      <h2 className="text-lg sm:text-xl md:text-2xl lg:text-[28px] font-bold mb-4 sm:mb-6 text-center font-lora">
        Our Categories
      </h2>
      <div className="relative">
        <Slider ref={sliderRef} {...settings}>
          {categories.map((category, index) => (
            <div key={index} className="px-2">
              <div
                style={{ width: 270, height: 270 }}
                className="relative overflow-hidden rounded-md m-2 sm:m-3"
              >
                <Image
                  src={category.src}
                  alt={category.alt}
                  className="object-cover"
                  fill
                  priority={index < 3}
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent py-2">
                  <p className="text-white text-sm sm:text-base md:text-lg font-lora font-semibold text-center px-2">
                    {category.text}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </Slider>
        <div className="absolute top-1/2 left-0 right-0 flex justify-between transform -translate-y-1/2 px-2 sm:px-4 md:px-8 z-10">
          <Button
            variant="outline"
            size="icon"
            className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md h-8 w-8 sm:h-10 sm:w-10"
            onClick={goToPrev}
          >
            <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5 text-custom-red-500" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="bg-custom-blue-50 backdrop-blur-sm hover:bg-white border-none rounded-full shadow-md h-8 w-8 sm:h-10 sm:w-10"
            onClick={goToNext}
          >
            <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5 text-custom-red-500" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Categories;

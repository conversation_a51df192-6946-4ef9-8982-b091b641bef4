"use client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import clsx from "clsx";
import { Copy, Heart, MapPin } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

// Types for the product data
interface ProductImage {
  src: string;
  alt: string;
  width?: number;
  height?: number;
}

interface ProductSpecification {
  label: string;
  value: string;
}

interface ProductDimensions {
  length?: string;
  width?: string;
  height?: string;
  lengthInches?: string;
}

interface MaterialDetails {
  type: "gold" | "diamond" | "silver" | "platinum";
  icon: string;
  bgColor: string;
  iconBgColor: string;
  dimensions?: ProductDimensions;
  weight?: string;
  purity?: string;
  setting?: string;
  total?: string | number;
  totalWeight?: string;
}

interface ProductData {
  id: string;
  title: string;
  price: number;
  currency: string;
  taxInfo: string;
  category: string;
  description: string[];
  mainImage: ProductImage;
  thumbnailImages: ProductImage[];
  specifications: ProductSpecification[];
  sku: string;
  detailDescription: string;
  materials: MaterialDetails[];
  isCustomizable?: boolean;
  customizeButtonText?: string;
}

interface SingleProductProps {
  product?: ProductData;
  onAddToCart?: (productId: string) => void;
  onAddToWishlist?: (productId: string) => void;
  onFindStore?: (productId: string) => void;
  onCustomize?: (productId: string) => void;
  onCopySku?: (sku: string) => void;
}

// Default product data for fallback
const defaultProduct: ProductData = {
  id: "default-product",
  title: "Enticing Petite Drop Earrings",
  price: 75000,
  currency: "NRP",
  taxInfo: "Incl. taxes & Charges",
  category: "Women",
  description: [
    "Add subtle grace to your outfit with these enticing drop earrings crafted in 18 Karat Yellow Gold with a glossy finish.",
    "Cast a spell of enchantment on everyone with these stunning petite danglers. It's the perfect addition to your ethnic attire.",
  ],
  mainImage: {
    src: "/images/main-product.png",
    alt: "Woman wearing gold drop earrings",
    width: 600,
    height: 600,
  },
  thumbnailImages: [
    {
      src: "/placeholder.svg?height=200&width=200",
      alt: "Close-up of earrings",
      width: 200,
      height: 200,
    },
    {
      src: "/placeholder.svg?height=200&width=200",
      alt: "Earrings in jewelry box",
      width: 200,
      height: 200,
    },
    {
      src: "/placeholder.svg?height=200&width=200",
      alt: "Product detail",
      width: 200,
      height: 200,
    },
  ],
  specifications: [
    { label: "Size", value: "12 (51.8 mm)" },
    { label: "Gold", value: "18 KT_Yellow" },
    { label: "Diamond", value: "FG - SI" },
  ],
  sku: "UT01107-1R0000",
  detailDescription:
    "Set in 9 KT Yellow Gold(1.030 g) with diamonds (0.032 ct ,FG-SI)",
  materials: [
    {
      type: "gold",
      icon: "Au",
      bgColor: "bg-orange-50",
      iconBgColor: "bg-yellow-500",
      dimensions: {
        length: "17.36cm",
        lengthInches: "(7.25 inches)",
        width: "16.87mm",
        height: "12.84 mm",
      },
      weight: "Gross: 0.0670 g",
      purity: "14 KT",
    },
    {
      type: "diamond",
      icon: "♦",
      bgColor: "bg-blue-50",
      iconBgColor: "bg-blue-500",
      purity: "FG - SI",
      setting: "Setting : Micro Prong",
      total: 9,
      totalWeight: "0.040 ct",
    },
  ],
  isCustomizable: true,
  customizeButtonText: "CUSTOMIZE",
};

export default function SingleProduct({
  product = defaultProduct,
  onAddToCart,
  onAddToWishlist,
  onFindStore,
  onCustomize,
  onCopySku,
}: SingleProductProps) {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const handleCopySku = () => {
    navigator.clipboard.writeText(product.sku);
    onCopySku?.(product.sku);
  };

  const handleAddToCart = () => {
    onAddToCart?.(product.id);
  };

  const handleAddToWishlist = () => {
    onAddToWishlist?.(product.id);
  };

  const handleFindStore = () => {
    onFindStore?.(product.id);
  };

  const handleCustomize = () => {
    onCustomize?.(product.id);
  };
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="grid lg:grid-cols-2 gap-8">
        {/* Left side - Product Images */}
        <div className="space-y-4">
          {/* Main product image */}
          <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
            <Image
              src={
                selectedImageIndex === -1
                  ? product.mainImage.src
                  : product.thumbnailImages[selectedImageIndex]?.src ||
                    product.mainImage.src
              }
              alt={
                selectedImageIndex === -1
                  ? product.mainImage.alt
                  : product.thumbnailImages[selectedImageIndex]?.alt ||
                    product.mainImage.alt
              }
              width={product.mainImage.width || 600}
              height={product.mainImage.height || 600}
              className="w-full h-full object-cover cursor-pointer"
              onClick={() => setSelectedImageIndex(-1)}
            />
          </div>

          {/* Thumbnail images */}
          <div
            className={`grid gap-4 ${
              product.thumbnailImages.length <= 3
                ? "grid-cols-3"
                : "grid-cols-4"
            }`}
          >
            {product.thumbnailImages.map((image, index) => (
              <div
                key={index}
                className={`aspect-square overflow-hidden rounded-lg bg-gray-100 cursor-pointer border-2 transition-all ${
                  selectedImageIndex === index
                    ? "border-custom-red-500"
                    : "border-transparent hover:border-gray-300"
                }`}
                onClick={() => setSelectedImageIndex(index)}
              >
                <Image
                  src={image.src}
                  alt={image.alt}
                  width={image.width || 200}
                  height={image.height || 200}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Right side - Product Details */}
        <div className="space-y-6">
          {/* Product Title and Price */}
          <div className="space-y-4">
            <h1 className="text-3xl font-bold text-custom-red-500">
              {product.title}
            </h1>

            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold text-custom-red-500">
                {product.currency} {product.price.toLocaleString()}
              </span>
              <span className="text-sm text-gray-500">{product.taxInfo}</span>
            </div>

            <Badge
              variant="outline"
              className="text-custom-red-500 border-red-200 bg-red-50"
            >
              {product.category}
            </Badge>
          </div>

          {/* Product Description */}
          <div className="space-y-4">
            {product.description.map((paragraph, index) => (
              <p key={index} className="text-gray-700 leading-relaxed">
                {paragraph}
              </p>
            ))}
          </div>

          {/* Specifications */}
          <div className="flex *:w-1/4 items-center border rounded-lg *:p-2 divide-x ">
            {product.specifications.map((spec, index) => (
              <div className="" key={index}>
                <span className="text-gray-500 text-sm">{spec.label}</span>
                <p className="font-semibold ">{spec.value}</p>
              </div>
            ))}
            {product.isCustomizable && (
              <div
                className="bg-custom-yellow-500 h-full"
                onClick={handleCustomize}
              >
                {product.customizeButtonText || "CUSTOMIZE"}
              </div>
            )}
          </div>

          {/* Product Details Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-custom-red-500">
              Product Details
            </h3>

            <div className="flex items-center gap-2 text-sm text-custom-blue-500">
              <span>SKU {product.sku}</span>
              <div title="Copy SKU">
                <Copy
                  className="w-4 h-4 cursor-pointer hover:text-custom-red-500 transition-colors"
                  onClick={handleCopySku}
                />
              </div>
            </div>

            <p className="text-sm text-gray-700">{product.detailDescription}</p>
          </div>

          {/* Specifications Tables */}
          <div className="space-y-4">
            {product.materials.map((material, index) => (
              <Card
                key={index}
                className={clsx(
                  material.bgColor,
                  "p-0 overflow-hidden gap-y-0"
                )}
              >
                <div className="flex bg-[#FFE4D1] items-center gap-2 px-4 py-2">
                  <div
                    className={`w-6 h-6 ${material.iconBgColor} rounded-full flex items-center justify-center`}
                  >
                    <span className="text-xs text-white font-bold">
                      {material.icon}
                    </span>
                  </div>
                  <span className="font-medium capitalize">
                    {material.type}
                  </span>
                </div>
                <div className="flex p-4 divide-x *:w-1/3 gap-4 text-sm">
                  {/* Dimensions */}
                  {material.dimensions && (
                    <div>
                      <span className="text-gray-600">Dimensions</span>
                      {material.dimensions.length && (
                        <p className="font-medium">
                          Length: {material.dimensions.length}
                        </p>
                      )}
                      {material.dimensions.lengthInches && (
                        <p className="text-gray-500">
                          {material.dimensions.lengthInches}
                        </p>
                      )}
                      {material.dimensions.width && (
                        <p className="font-medium">
                          Width: {material.dimensions.width}
                        </p>
                      )}
                      {material.dimensions.height && (
                        <p className="font-medium">
                          Height: {material.dimensions.height}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Weight */}
                  {material.weight && (
                    <div>
                      <span className="text-gray-600">Weight</span>
                      <p className="font-medium">{material.weight}</p>
                    </div>
                  )}

                  {/* Purity */}
                  {material.purity && (
                    <div>
                      <span className="text-gray-600">
                        {material.type === "diamond" ? "Type" : "Purity"}
                      </span>
                      <p className="font-medium">{material.purity}</p>
                    </div>
                  )}

                  {/* Setting (for diamonds) */}
                  {material.setting && (
                    <div>
                      <span className="text-gray-600">Setting</span>
                      <p className="font-medium">{material.setting}</p>
                      {material.total && (
                        <p className="font-medium">Total : {material.total}</p>
                      )}
                    </div>
                  )}

                  {/* Total Weight (for diamonds) */}
                  {material.totalWeight && (
                    <div>
                      <span className="text-gray-600">Total Weight</span>
                      <p className="font-medium">{material.totalWeight}</p>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="text-center">
                <div
                  className="flex items-center justify-center gap-2 text-custom-red-500"
                  onClick={handleAddToWishlist}
                >
                  <Heart className="w-5 h-5" />
                  <span className="font-medium">Choose This Item</span>
                </div>
                <p className="text-sm text-gray-500 mt-1">Save for Later</p>
                <Button
                  className="w-full mt-3 bg-gradient-to-r from-custom-yellow-500 to-custom-yellow-700 hover:bg-yellow-600 text-black"
                  onClick={handleAddToCart}
                >
                  Add to cart
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="text-center">
                <div className="flex items-center justify-center gap-2 text-custom-red-500">
                  <MapPin className="w-5 h-5" />
                  <span className="font-medium">Store Availability</span>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Find Designs in store
                </p>
                <Button
                  variant="outline"
                  className="w-full mt-3 bg-gradient-to-r from-[#E14F60] to-[#F5794F] text-white hover:bg-red-50"
                  onClick={handleFindStore}
                >
                  Near By Store
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

// Export the types for use in other components
export type {
  ProductData,
  ProductImage,
  ProductSpecification,
  MaterialDetails,
  SingleProductProps,
};

"use client";

import React from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { InputField, TextareaField } from "@/components/ui/form-field";
import {
  RadioGroupField,
  CheckboxGroupField,
  CheckboxField,
  SelectField,
  type RadioOption,
  type CheckboxOption,
  type SelectOption,
} from "@/components/ui/extended-form-fields";

// Field types
type FieldType =
  | "text"
  | "email"
  | "password"
  | "number"
  | "tel"
  | "url"
  | "textarea"
  | "select"
  | "radio"
  | "checkbox"
  | "checkbox-group"
  | "date"
  | "time"
  | "datetime-local";

// Layout types
type LayoutType = "single" | "half" | "third" | "quarter" | "full";

// Base field configuration
interface BaseFieldConfig {
  name: string;
  type: FieldType;
  label?: string;
  description?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  size?: "sm" | "md" | "lg";
  layout?: LayoutType;
  className?: string;
  validation?: z.ZodType<any>;
}

// Input field configuration
interface InputFieldConfig extends BaseFieldConfig {
  type:
    | "text"
    | "email"
    | "password"
    | "number"
    | "tel"
    | "url"
    | "date"
    | "time"
    | "datetime-local";
  leftSection?: React.ReactNode;
  rightSection?: React.ReactNode;
  withPasswordToggle?: boolean;
  maxLength?: number;
  minLength?: number;
  min?: number;
  max?: number;
  step?: number;
}

// Textarea field configuration
interface TextareaFieldConfig extends BaseFieldConfig {
  type: "textarea";
  rows?: number;
  maxLength?: number;
  minLength?: number;
  resize?: "none" | "both" | "horizontal" | "vertical";
}

// Select field configuration
interface SelectFieldConfig extends BaseFieldConfig {
  type: "select";
  options: SelectOption[];
  searchPlaceholder?: string;
  emptyMessage?: string;
}

// Radio field configuration
interface RadioFieldConfig extends BaseFieldConfig {
  type: "radio";
  options: RadioOption[];
  orientation?: "horizontal" | "vertical";
}

// Checkbox group field configuration
interface CheckboxGroupFieldConfig extends BaseFieldConfig {
  type: "checkbox-group";
  options: CheckboxOption[];
  orientation?: "horizontal" | "vertical";
}

// Single checkbox field configuration
interface CheckboxFieldConfig extends BaseFieldConfig {
  type: "checkbox";
  checkboxLabel?: string;
}

// Union type for all field configurations
type FieldConfig =
  | InputFieldConfig
  | TextareaFieldConfig
  | SelectFieldConfig
  | RadioFieldConfig
  | CheckboxGroupFieldConfig
  | CheckboxFieldConfig;

// Form section configuration
interface FormSection {
  title?: string;
  description?: string;
  fields: FieldConfig[];
  layout?: "grid" | "flex";
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

// Form configuration
interface FormConfig {
  title?: string;
  description?: string;
  sections: FormSection[];
  submitButtonText?: string;
  resetButtonText?: string;
  showResetButton?: boolean;
  onSubmit: (data: any) => void | Promise<void>;
  onReset?: () => void;
  className?: string;
  schema?: z.ZodSchema<any>;
  defaultValues?: Record<string, any>;
}

// Layout class mapping
const layoutClasses: Record<LayoutType, string> = {
  single: "col-span-1",
  half: "col-span-1 md:col-span-1",
  third: "col-span-1 md:col-span-1 lg:col-span-1",
  quarter: "col-span-1",
  full: "col-span-full",
};

// Grid column classes
const gridColumnClasses = {
  1: "grid-cols-1",
  2: "grid-cols-1 md:grid-cols-2",
  3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
};

// Dynamic Form Renderer Component
const DynamicFormRenderer = ({
  title,
  description,
  sections,
  submitButtonText = "Submit",
  resetButtonText = "Reset",
  showResetButton = true,
  onSubmit,
  onReset,
  className,
  schema,
  defaultValues = {},
}: FormConfig) => {
  // Generate default schema if not provided
  const generateDefaultSchema = (): z.ZodSchema<any> => {
    const schemaFields: Record<string, z.ZodTypeAny> = {};

    sections.forEach((section) => {
      section.fields.forEach((field) => {
        if (field.validation) {
          schemaFields[field.name] = field.validation;
        } else {
          // Generate basic validation based on field type
          let fieldSchema: z.ZodTypeAny;

          switch (field.type) {
            case "email":
              fieldSchema = z
                .string()
                .regex(
                  /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  "Please enter a valid email address"
                );
              break;
            case "number":
              fieldSchema = z.number();
              break;
            case "checkbox":
              fieldSchema = z.boolean();
              break;
            case "checkbox-group":
              fieldSchema = z.array(z.string());
              break;
            default:
              fieldSchema = z.string();
          }

          if (field.required) {
            if (field.type === "checkbox") {
              fieldSchema = (fieldSchema as z.ZodBoolean).refine(
                (val) => val === true,
                {
                  message: `${field.label || field.name} is required`,
                }
              );
            } else if (field.type === "checkbox-group") {
              fieldSchema = (fieldSchema as z.ZodArray<any>).min(
                1,
                `Please select at least one ${field.label || field.name}`
              );
            } else {
              fieldSchema = (fieldSchema as z.ZodString).min(
                1,
                `${field.label || field.name} is required`
              );
            }
          } else {
            if (field.type !== "checkbox" && field.type !== "checkbox-group") {
              fieldSchema = fieldSchema.optional();
            }
          }

          schemaFields[field.name] = fieldSchema;
        }
      });
    });

    return z.object(schemaFields);
  };

  const formSchema = schema || generateDefaultSchema();

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting, isValid },
  } = useForm({
    resolver: zodResolver(formSchema as any),
    defaultValues: defaultValues,
    mode: "onChange",
  });

  // Handle form submission
  const onFormSubmit = async (data: any) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  // Handle form reset
  const handleReset = () => {
    reset();
    onReset?.();
  };

  // Render individual field
  const renderField = (field: FieldConfig) => {
    const fieldError = errors[field.name]?.message as string;

    const commonProps = {
      label: field.label,
      description: field.description,
      error: fieldError,
      required: field.required,
      disabled: field.disabled,
      size: field.size,
      className: field.className,
    };

    return (
      <Controller
        key={field.name}
        name={field.name}
        control={control}
        render={({ field: controllerField }) => {
          switch (field.type) {
            case "text":
            case "email":
            case "password":
            case "tel":
            case "url":
            case "date":
            case "time":
            case "datetime-local":
              const inputField = field as InputFieldConfig;
              return (
                <InputField
                  {...controllerField}
                  {...commonProps}
                  type={field.type}
                  placeholder={field.placeholder}
                  leftSection={inputField.leftSection}
                  rightSection={inputField.rightSection}
                  withPasswordToggle={inputField.withPasswordToggle}
                  maxLength={inputField.maxLength}
                />
              );

            case "number":
              const numberField = field as InputFieldConfig;
              return (
                <InputField
                  {...controllerField}
                  {...commonProps}
                  type="number"
                  placeholder={field.placeholder}
                  onChange={(e) =>
                    controllerField.onChange(Number(e.target.value) || 0)
                  }
                />
              );

            case "textarea":
              const textareaField = field as TextareaFieldConfig;
              return (
                <TextareaField
                  {...controllerField}
                  {...commonProps}
                  placeholder={field.placeholder}
                  rows={textareaField.rows}
                  resize={textareaField.resize}
                  maxLength={textareaField.maxLength}
                />
              );

            case "select":
              const selectField = field as SelectFieldConfig;
              return (
                <SelectField
                  {...controllerField}
                  {...commonProps}
                  options={selectField.options}
                  placeholder={field.placeholder}
                  searchPlaceholder={selectField.searchPlaceholder}
                  emptyMessage={selectField.emptyMessage}
                />
              );

            case "radio":
              const radioField = field as RadioFieldConfig;
              return (
                <RadioGroupField
                  {...controllerField}
                  {...commonProps}
                  options={radioField.options}
                  orientation={radioField.orientation}
                />
              );

            case "checkbox-group":
              const checkboxGroupField = field as CheckboxGroupFieldConfig;
              return (
                <CheckboxGroupField
                  {...controllerField}
                  {...commonProps}
                  options={checkboxGroupField.options}
                  orientation={checkboxGroupField.orientation}
                />
              );

            case "checkbox":
              const checkboxField = field as CheckboxFieldConfig;
              return (
                <CheckboxField
                  {...controllerField}
                  {...commonProps}
                  checkboxLabel={checkboxField.checkboxLabel}
                />
              );

            default:
              return <div>Unsupported field type: {(field as any).type}</div>;
          }
        }}
      />
    );
  };

  // Render form section
  const renderSection = (section: FormSection, sectionIndex: number) => {
    const columns = section.columns || 2;
    const gridClass = gridColumnClasses[columns];

    return (
      <div key={sectionIndex} className={cn("space-y-6", section.className)}>
        {/* Section Header */}
        {(section.title || section.description) && (
          <div className="space-y-2">
            {section.title && (
              <h3 className="text-lg font-semibold text-custom-red-500">
                {section.title}
              </h3>
            )}
            {section.description && (
              <p className="text-sm text-muted-foreground">
                {section.description}
              </p>
            )}
          </div>
        )}

        {/* Section Fields */}
        <div
          className={cn(
            section.layout === "flex"
              ? "flex flex-wrap gap-4"
              : `grid gap-6 ${gridClass}`
          )}
        >
          {section.fields.map((field) => (
            <div
              key={field.name}
              className={cn(
                section.layout === "grid" && field.layout
                  ? layoutClasses[field.layout]
                  : ""
              )}
            >
              {renderField(field)}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Card className={cn("w-full max-w-4xl mx-auto", className)}>
      {/* Form Header */}
      {(title || description) && (
        <CardHeader>
          {title && (
            <CardTitle className="text-2xl text-custom-red-500">
              {title}
            </CardTitle>
          )}
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </CardHeader>
      )}

      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-8">
          {/* Form Sections */}
          {sections.map((section, index) => renderSection(section, index))}

          {/* Form Actions */}
          <div className="flex gap-4 pt-6 border-t">
            <Button
              type="submit"
              disabled={!isValid || isSubmitting}
              loading={isSubmitting}
              loadingText="Submitting..."
              className="bg-custom-red-500 hover:bg-custom-red-600"
            >
              {submitButtonText}
            </Button>

            {showResetButton && (
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={isSubmitting}
              >
                {resetButtonText}
              </Button>
            )}
          </div>

          {/* Form Validation Summary */}
          {!isValid && Object.keys(errors).length > 0 && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-custom-red-500 font-medium mb-2">
                Please fix the following errors:
              </p>
              <ul className="text-xs text-custom-red-500 list-disc list-inside space-y-1">
                {Object.entries(errors).map(([field, error]) => (
                  <li key={field}>
                    <strong>{field}:</strong> {error?.message as string}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export {
  DynamicFormRenderer,
  type FormConfig,
  type FormSection,
  type FieldConfig,
  type InputFieldConfig,
  type TextareaFieldConfig,
  type SelectFieldConfig,
  type RadioFieldConfig,
  type CheckboxGroupFieldConfig,
  type CheckboxFieldConfig,
  type FieldType,
  type LayoutType,
};

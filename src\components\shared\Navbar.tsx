"use client";

import { useState } from "react";
import {
  Search,
  MapPin,
  User,
  Heart,
  ShoppingCart,
  Menu,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const navigationItems = [
    "All Jewellery",
    "Gold",
    "Diamond",
    "Earrings",
    "Rings",
    "Daily Wears",
    "Wedding",
  ];

  return (
    <header className="w-full bg-custom-violet-50 text-white">
      {/* Top Header */}
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-red-500">RIGHT SONI</h1>
          </div>

          {/* Search Bar - Hidden on mobile */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div className="relative w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white text-black border-1 border-[#D1D1D1] rounded-md"
              />
            </div>
          </div>

          {/* Right Actions */}
          <div className="flex items-center space-x-4">
            {/* Stores Button - Hidden on mobile */}
            <Button
              variant="outline"
              className="hidden md:flex items-center space-x-2 border-custom-red-500 text-black"
            >
              <MapPin className="w-4 h-4 text-custom-red-500" />
              <span>Stores</span>
            </Button>

            {/* User Actions */}
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                className=" hover:bg-purple-700"
              >
                <User className="size-4 text-black" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className=" hover:bg-purple-700 relative"
              >
                <Heart className="size-4 text-black" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-4 h-4 flex items-center justify-center text-white">
                  2
                </span>
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className=" hover:bg-purple-700 relative"
              >
                <ShoppingCart className="size-4 text-black" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-4 h-4 flex items-center justify-center text-white">
                  3
                </span>
              </Button>
            </div>

            {/* Mobile Menu Toggle */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-white hover:bg-purple-700"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? (
                <X className="size-4" />
              ) : (
                <Menu className="size-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden mt-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white text-black border-0 rounded-md w-full"
            />
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav
        className={`${isMenuOpen ? "block" : "hidden"} md:block bg-[#A91212]`}
      >
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-center space-y-2 md:space-y-0 md:space-x-8 py-2">
            {navigationItems.map((item, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-custom-red-500 hover:text-white justify-start md:justify-center px-3 py-2 text-sm font-medium transition-colors duration-200"
                onClick={() => {
                  console.log(`Navigating to ${item}`);
                  setIsMenuOpen(false);
                }}
              >
                {item}
              </Button>
            ))}
          </div>
        </div>

        {/* Mobile Stores Button */}
        <div className="md:hidden px-4 pb-3">
          <Button className="w-full bg-red-500 hover:bg-red-600 text-white flex items-center justify-center space-x-2">
            <MapPin className="w-4 h-4" />
            <span>Find Stores</span>
          </Button>
        </div>
      </nav>
    </header>
  );
}

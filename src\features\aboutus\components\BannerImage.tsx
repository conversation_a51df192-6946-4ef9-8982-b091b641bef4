// import Link from "next/link";
// import React from "react";

// interface BannerProps {
//   imageSrc: string;
//   title: string;
//   topTitle: string;
//   buttonText: string;
//   to: string;
// }

// const Banner: React.FC<BannerProps> = ({
//   imageSrc,
//   title,
//   topTitle,
//   buttonText,
//   to,
// }) => {
//   return (
//     <div
//       className="relative w-full h-64 bg-cover bg-start"
//       style={{ backgroundImage: `url(${imageSrc})` }}
//     >
//       <div className="relative flex items-center justify-end h-full px-6 md:px-12">
//         <div className="text-white font-lora  space-y-4">
//           <p className="font-semibold text-sm lg:text-base text-start">
//             {topTitle}
//           </p>
//           <h2 className="text-3xl md:text-4xl lg:text-[40px] font-bold mt-2 md:max-w-2xl lg:max-w-3xl">
//             {title}
//           </h2>
//           <Link
//             href={to}
//             className="mt-4 px-4 py-2    border-white border-2 hover:bg-opacity-30 text-white rounded"
//           >
//             {buttonText}
//           </Link>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Banner;

// Second

import Link from "next/link";
import React from "react";

interface BannerProps {
  imageSrc: string;
  title: string;
  topTitle: string;
  buttonText: string;
  description?: string;
  to: string;
}

const Banner: React.FC<BannerProps> = ({
  imageSrc,
  title,
  topTitle,
  buttonText,
  description,
  to,
}) => {
  return (
    <div
      className="relative w-full h-64 bg-cover bg-start"
      style={{ backgroundImage: `url(${imageSrc})` }}
    >
      <div className="absolute inset-0 "></div>
      <div className="relative flex items-center justify-end h-full px-6 md:px-12">
        <div className="text-white font-lora space-y-2">
          <p className="font-semibold text-sm lg:text-base text-start">
            {topTitle}
          </p>
          <h2 className="text-3xl md:text-4xl lg:text-[40px] font-bold mt-2 md:max-w-2xl lg:max-w-3xl">
            {title}
          </h2>
          {description && <p className="text-xs">{description}</p>}
          <div className="relative">
            <Link
              href={to}
              className="mt-4 inline-block px-6 md:px-8 lg:px-10 py-2 border-white border-2 hover:bg-opacity-30 text-white rounded cursor-pointer" // Increased py-3 for height
            >
              {buttonText}
            </Link>
            {/* <span className="absolute inset-0"></span>{" "} */}
            {/* Invisible overlay to expand clickable area */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Banner;
